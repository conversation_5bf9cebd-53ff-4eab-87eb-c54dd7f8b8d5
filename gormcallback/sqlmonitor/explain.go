package sqlmonitor

import (
	"time"

	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/golang/fmonitor/pkg/metric"
	"gitlab.futunn.com/golang/fmonitor/pkg/metric/label"
	"gitlab.futunn.com/infra/frpc/pkg/thirdparty/db/gorm"
	"gitlab.futunn.com/web_data_application/golib/util/log"
	"gorm.io/gorm/logger"
)

type ExplainResult struct {
	Id           int64   `gorm:"column:id"`
	SelectType   string  `gorm:"column:select_type"`   // 查询行为类型 simple primary union...
	Table        string  `gorm:"column:table"`         // tableName
	Partitions   string  `gorm:"column:partitions"`    // 分区
	Type         string  `gorm:"column:type"`          // 引擎层查询数据行为类型 system const ref index index_merge all ...
	PossibleKeys string  `gorm:"column:possible_keys"` // 可能用到的所有索引
	Key          string  `gorm:"column:key"`           // 真正用到的所有索引
	KeyLen       int32   `gorm:"column:key_len"`       // 查询时用到的索引长度
	Ref          string  `gorm:"column:ref"`           // 哪些列或常量与key所使用的字段进行比较
	Rows         int     `gorm:"column:rows"`          // 预估需要扫描的行数
	Filtered     float32 `gorm:"column:filtered"`      // 根据条件过滤后剩余的行数百分比（预估）
	Extra        string  `gorm:"column:extra"`
}

func explainSQL(config *Config, db *gorm.DB, cost time.Duration, metricLabels []string) {
	var result ExplainResult
	// 为了避免影响原查询，这里使用新的DB对象
	err := db.Session(&gorm.Session{
		NewDB:   true,
		Context: db.Statement.Context,
	}).Raw("EXPLAIN "+db.Statement.SQL.String(), db.Statement.Vars...).Scan(&result).Error
	if err != nil {
		log.Error(db.Statement.Context, "query_explain_error",
			fls.String("sql", logger.ExplainSQL(db.Statement.SQL.String(), nil, `"`, db.Statement.Vars...)),
			fls.ErrorField(err),
		).WithInc()
		return
	}

	analyzeExplainResult(config, result, cost, metricLabels, db)
}

func analyzeExplainResult(config *Config, explainResult ExplainResult, cost time.Duration, metricLabels []string, db *gorm.DB) {
	// 排查一些框架行为，如select version()
	if explainResult.Table == "" {
		return
	}

	metricLabels = append(metricLabels,
		label.EventLevel, "alert",
		"query_issue1", "query_cost_too_much_time",
	)

	// 满足以下条件可认为查询有性能问题
	// 1. 查询行数超过阈值
	// 2. 未命中索引
	if explainResult.Rows > config.MaxScanRows {
		log.Warn(db.Statement.Context, "query_scan_too_many_rows",
			fls.String("table", db.Statement.Table),
			fls.String("sql", logger.ExplainSQL(db.Statement.SQL.String(), nil, `"`, db.Statement.Vars...)),
			fls.Any("explain_result", explainResult),
			fls.Duration("cost", cost),
		)
		if config.PanicWhenMissedIndex {
			panic("query_scan_too_many_rows")
		}
		metricLabels = append(metricLabels, "query_issue2", "query_scan_too_many_rows")
	}

	if explainResult.Key == "" || explainResult.Type == "ALL" {
		log.Error(db.Statement.Context, "query_missed_index",
			fls.String("table", db.Statement.Table),
			fls.String("sql", logger.ExplainSQL(db.Statement.SQL.String(), nil, `"`, db.Statement.Vars...)),
			fls.Any("explain_result", explainResult),
			fls.Duration("cost", cost),
		)
		if config.PanicWhenMissedIndex {
			panic("query_missed_index")
		}
		metricLabels = append(metricLabels, "query_issue3", "query_missed_index")
	}

	// 上报查询耗时信息
	metric.DBCallDuration.WithLabels(metricLabels...).Update(cost.Seconds())
}
