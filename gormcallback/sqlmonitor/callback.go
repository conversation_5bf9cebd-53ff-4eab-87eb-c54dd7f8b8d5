// Package sqlmonitor
// 用于监控sql执行耗时，以及对耗时超过阈值的sql进行explain等
package sqlmonitor

import (
	"math/rand"
	"sync"
	"time"

	"gitlab.futunn.com/golang/fmonitor/pkg/metric"
	"gitlab.futunn.com/infra/frpc/pkg/thirdparty/db/gorm"
)

// GetSQLMonitorCallBack gorm callback
// 功能： 1. sql执行耗时上报，2. 对耗时超过阈值对sql进行explain，并进行性能分析
// usage:
// 有两种方式使用该中间件
// 注意：方式1由于frpc初始化顺序问题，所以自行解析了配置文件，使用限制是配置文件路径必须是conf/conf.toml！！！
// 1. 通过配置文件开启，只需要在配置文件中配置即可。同时在main包中导入本包，使本包init函数生效即可
// [gorm_callback.sql_monitor]
// conf_key = "your_db_conf_key"
// enabled = true
// monitor_all_tables = false
// monitor_specified_tables = ["table1", "table2"]
// explain_sample_rate = 0.1
// max_exec_time = "300ms"
// max_scan_rows = 1000
// panic_when_missed_index = false
//
//  2. 在代码里手动注册
//
// func init() {
// gorm.RegisterCallbackFunc("your config key",
//
//	GetSQLMonitorCallBack(
//			WithEnabled(true), // 开启explain功能
//			WithMonitorAllTables(false), // 对所有表进行监控
//			WithMonitorSpecifiedTables("table1", "table2"), // 指定需要监控的表
//
// 以下配置对explain生效
//
//			WithExplainSampleRate(0.1), // 采样率，0-1之间，默认是0.001
//			WithMaxExecTime(1*time.Second), // SQL执行的最大时间，不填默认300ms
//			WithMaxScanRows(5000), // SQL执行的最大行数，不填默认1000行
//			WithPanicWhenMissedIndex(true), // 未命中索引时是否panic
//	),
//	)
//	}
func GetSQLMonitorCallBack(opts ...Option) func(configKey string, config *gorm.Config, db *gorm.DB) {
	c := defaultConfig()
	for _, opt := range opts {
		opt(&c)
	}

	return func(configKey string, config *gorm.Config, db *gorm.DB) {
		registerSQLMonitor(&c, db)
	}
}

func registerSQLMonitor(config *Config, db *gorm.DB) {
	if !config.Enabled {
		return
	}

	if !config.MonitorAllTables && len(config.MonitorSpecifiedTables) == 0 {
		return
	}

	_ = db.Callback().Query().Before("gorm:query").Register("sql_monitor_start", func(db *gorm.DB) {
		db.InstanceSet("xxxx_sql_monitor_start_time", time.Now())
	})

	_ = db.Callback().Query().After("gorm:query").Register("sql_monitor_check", func(db *gorm.DB) {
		var cost time.Duration
		sqlMonitorStartTime, ok := db.InstanceGet("xxxx_sql_monitor_start_time")
		if ok {
			cost = time.Since(sqlMonitorStartTime.(time.Time))
		}

		// 收集metric标签
		metricLabels := []string{
			"metric_source", "sql_monitor",
			"table", db.Statement.Table,
			"sql", db.Statement.SQL.String(),
		}

		opType := judgeOperateType(config, db.Statement.Table, db.Statement.SQL.String(), cost)

		switch opType {
		case operateTypeReportCost:
			// 仅上报查询耗时信息
			metric.DBCallDuration.WithLabels(metricLabels...).Update(cost.Seconds())
		case operateTypeExplainSQL:
			// explain SQL且上报
			explainSQL(config, db, cost, metricLabels)
		}
	})
}

var firstTimeExplainRecords = sync.Map{}

type operateType int

const (
	operateTypeNone       operateType = 0
	operateTypeReportCost operateType = 1
	operateTypeExplainSQL operateType = 2
)

func judgeOperateType(config *Config, table string, sql string, cost time.Duration) operateType {
	result := operateTypeNone

	// 配置了对所有表进行监控，至少要报告耗时
	if config.MonitorAllTables {
		result = operateTypeReportCost
	}
	for _, t := range config.MonitorSpecifiedTables {
		if t == table {
			result = operateTypeReportCost
		}
	}
	if result == operateTypeNone {
		return result
	}

	// 超出最大执行时间，则判断需不需要进行explain
	// 1. 首次出现必explain
	// 2. 后续出现根据采样率决定是否explain
	if cost > config.MaxExecTime {
		// 采样率
		if config.ExplainSampleRate >= 1 {
			result = operateTypeExplainSQL
		} else {
			r := rand.New(rand.NewSource(time.Now().UnixNano()))
			// 生成一个0到1之间的浮点数
			if randomValue := r.Float64(); randomValue <= config.ExplainSampleRate {
				result = operateTypeExplainSQL
			}

			// 保底措施，首次出现必explain
			if result != operateTypeExplainSQL {
				if _, ok := firstTimeExplainRecords.Load(sql); !ok {
					result = operateTypeExplainSQL
					firstTimeExplainRecords.Store(sql, struct{}{})
				}
			}
		}
	}

	return result
}
