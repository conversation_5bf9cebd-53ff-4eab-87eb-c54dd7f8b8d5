package sqlmonitor

import (
	"context"

	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/infra/frpc/pkg/conf"
	"gitlab.futunn.com/infra/frpc/pkg/thirdparty/db/gorm"
	"gitlab.futunn.com/infra/frpc/pkg/util/pathconv"
	"gitlab.futunn.com/web_data_application/golib/util/log"
)

func init() {
	if err := autoRegister(); err != nil {
		log.Error(context.Background(),
			"failed_to_auto_register_sql_monitor",
			fls.ErrorField(err),
		).WithInc()
	}
}

func autoRegister() error {
	cfg := defaultConfig()

	// 这里为什么要用conf.NewConfig()，而不是直接使用conf.DefaultConfig()
	// 因为使用Default必须在pkg.Register之后才能读取到配置
	// 而gorm.RegisterCallbackFunc是在pkg.Register之前调用的
	// 所以这里自己创建一个config对象，然后读取配置
	// 缺点是写死了配置文件路径，必须是conf/conf.toml
	tomlConfig := conf.NewConfig()
	if err := tomlConfig.LoadFile(pathconv.AdjustPath("conf/conf.toml")); err != nil {
		return err
	}
	if err := tomlConfig.Unmarshal("gorm_callback.sql_monitor", &cfg); err != nil {
		return err
	}

	if cfg.Enabled {
		gorm.RegisterCallbackFunc(cfg.ConfKey, func(configKey string, config *gorm.Config, db *gorm.DB) {
			registerSQLMonitor(&cfg, db)
		})
	}

	return nil
}
