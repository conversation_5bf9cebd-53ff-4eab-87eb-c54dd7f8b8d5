package sqlmonitor

import "time"

// Config sqlmonitor插件配置
type Config struct {
	ConfKey string `toml:"conf_key"` // 配置key，对应db配置的conf_key
	// 开关控制，对耗时上报和explain都起作用
	Enabled                bool     `toml:"enabled"`                  // 是否开启该插件，默认false
	MonitorAllTables       bool     `toml:"monitor_all_tables"`       // 是否对所有表进行监控，包括耗时和explain，默认false
	MonitorSpecifiedTables []string `toml:"monitor_specified_tables"` // 指定需要监控的表，默认不监控
	// 以下参数对explain起作用
	ExplainSampleRate    float64       `toml:"explain_sample_rate"`     // explain采样率，0-1之间，默认0.001
	MaxExecTime          time.Duration `toml:"max_exec_time"`           // sql最大执行时间，默认300ms
	MaxScanRows          int           `toml:"max_scan_rows"`           // sql最大扫描行数，默认1000
	PanicWhenMissedIndex bool          `toml:"panic_when_missed_index"` // 未命中索引时是否panic，默认false
}

func defaultConfig() Config {
	return Config{
		ExplainSampleRate: 0.001,                  // explain采样率，0-1之间，默认0.001
		MaxExecTime:       300 * time.Millisecond, // sql最大执行时间，默认300ms
		MaxScanRows:       1000,                   // sql最大扫描行数，默认1000
	}
}

type Option func(*Config)

func WithEnabled(enabled bool) Option {
	return func(option *Config) {
		option.Enabled = enabled
	}
}

func WithMonitorAllTables(monitorTables bool) Option {
	return func(option *Config) {
		option.MonitorAllTables = monitorTables
	}
}

func WithMonitorSpecifiedTables(monitorSpecifiedTables ...string) Option {
	return func(option *Config) {
		option.MonitorSpecifiedTables = monitorSpecifiedTables
	}
}

func WithExplainSampleRate(explainSampleRate float64) Option {
	return func(option *Config) {
		option.ExplainSampleRate = explainSampleRate
	}
}

func WithMaxExecTime(maxExecTime time.Duration) Option {
	return func(option *Config) {
		option.MaxExecTime = maxExecTime
	}
}

func WithMaxScanRows(maxScanRows int) Option {
	return func(option *Config) {
		option.MaxScanRows = maxScanRows
	}
}

func WithPanicWhenMissedIndex(panicWhenMissedIndex bool) Option {
	return func(option *Config) {
		option.PanicWhenMissedIndex = panicWhenMissedIndex
	}
}
