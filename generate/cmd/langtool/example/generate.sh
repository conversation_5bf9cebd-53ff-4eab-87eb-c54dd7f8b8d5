# 生成多语言po文件
current_dir=$(pwd)
echo $current_dir
# -b 多语言后台的业务ID
# -p 生成的po文件存放目录
go run $current_dir/../main.go genpo \
-b 10036 \
-p $current_dir/locale

# 根据po文件生成go常量定义
output=$current_dir/stringid/stringid.go
po_file=$current_dir/locale/zh_CN/LC_MESSAGES/auto-gen.po
# -c 常量名前缀
# -i 输入的多语言po文件路径
# -o 输出的go文件路径
# -p 输出的go文件包名
go run $current_dir/../main.go constdef \
-c StringID \
-i $po_file \
-o $output \
-p langid