// Code generated by Lang Tool. DO NOT EDIT.
package langid

const (
		StringID194568 = "194568" /** 尊敬的客户，您的 */
		StringID194569 = "194569" /** 行情权限还剩 */
		StringID194570 = "194570" /** 天到期，如需续期，请前往： */
		StringID194571 = "194571" /** 尊敬的{{nick}}，您免费获得的 */
		StringID194572 = "194572" /** 已发货，卡号： */
		StringID194573 = "194573" /** , 密码： */
		StringID194574 = "194574" /** 如需激活请前往 */
		StringID194575 = "194575" /** 尊敬的客户，您已成功启用卡号为 */
		StringID194576 = "194576" /** 的行情卡，重启富途牛牛客户端后即可体验 */
		StringID194577 = "194577" /** 的行情卡，重启富途牛牛客户端后即可体验您所购买的 */
		StringID194578 = "194578" /** 尊敬的客户，您已成功购买了 */
		StringID194579 = "194579" /** 行情卡，我们已为您自动激活，重启牛牛客户端后即可体验 */
		StringID194580 = "194580" /** 行情 */
		StringID194581 = "194581" /** 很抱歉，您购买的 */
		StringID194582 = "194582" /** 行情卡的支付金额我们尚未收到。麻烦您查看下您的账号或购买流程上是否正确，如有疑问，请及时与我们联系。 */
		StringID194583 = "194583" /** 尊敬的客户，我们尚未收到您购买 */
		StringID194584 = "194584" /** 行情卡的款项，无法为您及时发放并激活行情卡。如有疑惑，请联系客服。 */
		StringID194585 = "194585" /** 月卡 */
		StringID194588 = "194588" /** 请选择行情卡 */
		StringID194589 = "194589" /** 牛牛号不能为空 */
		StringID194590 = "194590" /** 购买方式参数错误 */
		StringID194591 = "194591" /** 自动使用参数错误 */
		StringID194592 = "194592" /** 市场参数错误 */
		StringID194593 = "194593" /** 下单失败 */
		StringID194594 = "194594" /** 账户可用金额不足 */
		StringID194595 = "194595" /** 商品不存在 */
		StringID194596 = "194596" /** futu下单失败 */
		StringID194597 = "194597" /** 微信app下单失败 */
		StringID194599 = "194599" /** 美股期权实时行情 */
		StringID194600 = "194600" /** 纳斯达克Basic报价 */
		StringID194602 = "194602" /** 股票深度摆盘 */
		StringID194603 = "194603" /** 期权实时行情 */
		StringID194604 = "194604" /** 期权报价 */
		StringID194605 = "194605" /** BMP实时行情 */
		StringID194606 = "194606" /** Lv1基础行情 */
		StringID194607 = "194607" /** LV2高级行情 */
		StringID194608 = "194608" /** 月卡(30天) */
		StringID194609 = "194609" /** 季卡(90天) */
		StringID194610 = "194610" /** 年卡(360天) */
		StringID194612 = "194612" /** 天卡 */
		StringID194613 = "194613" /** 天 */
		StringID194614 = "194614" /** ip地址参数错误 */
		StringID194615 = "194615" /** 商品类型参数错误 */
		StringID194616 = "194616" /** 客户端类型参数错误 */
		StringID194617 = "194617" /** 实时行情使用声明 */
		StringID194618 = "194618" /** 人民币； */
		StringID194619 = "194619" /** 《自动续期订阅协议》 */
		StringID194620 = "194620" /** 《隐私政策》 */
		StringID194621 = "194621" /** 订单不存在或者已完成 */
		StringID194622 = "194622" /** 订单已失效 */
		StringID194623 = "194623" /** 订单不存在 */
		StringID194624 = "194624" /** 季卡 */
		StringID194625 = "194625" /** 年卡 */
		StringID194626 = "194626" /** 行情卡卡号错误 */
		StringID194627 = "194627" /** 行情卡密码错误 */
		StringID194628 = "194628" /** 行情卡已被使用 */
		StringID194629 = "194629" /** 行情卡已被销卡 */
		StringID194630 = "194630" /** 港股现金账户 */
		StringID194631 = "194631" /** 港股融资账户 */
		StringID194632 = "194632" /** 美股现金账户 */
		StringID194633 = "194633" /** 美股融资融券账户 */
		StringID194634 = "194634" /** A股通现金账户 */
		StringID194635 = "194635" /** A股通融资账户 */
		StringID194636 = "194636" /** 港股LV2行情 */
		StringID194640 = "194640" /** 美股期权深度摆盘 */
		StringID194641 = "194641" /** 提供全美综合逐笔成交和最佳买卖档数据 */
		StringID194642 = "194642" /** 显示NASDAQ的完整订单报价、市场参与者 */
		StringID194643 = "194643" /** 显示NYSE的完整订单报价 */
		StringID194644 = "194644" /** 显示全美综合逐笔成交和最佳买卖档数据 */
		StringID194645 = "194645" /** 显示美股期权逐笔成交和实时报价 */
		StringID194646 = "194646" /** 元 */
		StringID194647 = "194647" /** 美元 */
		StringID194648 = "194648" /** 港币 */
		StringID194649 = "194649" /** 所有商品 */
		StringID194651 = "194651" /** 牛牛号参数错误 */
		StringID194652 = "194652" /** 行情卡类型参数错误 */
		StringID194653 = "194653" /** 生成行情卡异常 */
		StringID194654 = "194654" /** 《实时行情免责声明》 */
		StringID194655 = "194655" /** 港股期权期货BMP */
		StringID194656 = "194656" /** 港股期权期货LV1 */
		StringID194657 = "194657" /** 港股期权期货LV2 */
		StringID194658 = "194658" /** 衍生品报价 */
		StringID194661 = "194661" /** 移动端 */
		StringID194662 = "194662" /** 桌面端 */
		StringID194664 = "194664" /** 专业投资者必备 */
		StringID194665 = "194665" /** 当前激活量已超限额，请于下月再激活 */
		StringID194666 = "194666" /** 期权深度摆盘 */
		StringID194667 = "194667" /** 期权深度行情 */
		StringID194668 = "194668" /** 深度摆盘 */
		StringID194669 = "194669" /** 连续包月 */
		StringID194670 = "194670" /** 连续包季 */
		StringID194671 = "194671" /** 连续包年 */
		StringID194672 = "194672" /** 连续包周 */
		StringID194673 = "194673" /** 周卡 */
		StringID194674 = "194674" /** 期货报价 */
		StringID194675 = "194675" /** 期货LV2高级行情 */
		StringID194676 = "194676" /** 期货LV1实时行情 */
		StringID194677 = "194677" /** 期货延时行情 */
		StringID194678 = "194678" /** 商品id不存在 */
		StringID194679 = "194679" /** 股票LV2高级行情 */
		StringID194680 = "194680" /** 股票LV1实时行情 */
		StringID194681 = "194681" /** 股票延时行情 */
		StringID194683 = "194683" /** 基本面投资者必备 */
		StringID194684 = "194684" /** 智能形态解读 */
		StringID194685 = "194685" /** 技术分析者必备 */
		StringID194687 = "194687" /** LV1实时行情 */
		StringID194690 = "194690" /** A股LV2行情 */
		StringID194695 = "194695" /** OPRA期权实时行情 */
		StringID194696 = "194696" /** 期权期货LV2高级行情 */
		StringID194699 = "194699" /** OPRA期权深度行情 */
		StringID194704 = "194704" /** Nasdaq Basic+TotalView */
		StringID194707 = "194707" /** 财务解读 */
		StringID194708 = "194708" /** CBOE Bats BZX */
		StringID194709 = "194709" /** CBOE Direct Edge */
		StringID194710 = "194710" /** 加拿大LV1实时行情 */
		StringID194711 = "194711" /** 港股LV1实时行情(移动端) */
		StringID194712 = "194712" /** 澳洲LV2高级行情 */
		StringID194713 = "194713" /** 港股LV2高级行情 */
		StringID194714 = "194714" /** 港股期权期货LV2行情 */
		StringID194716 = "194716" /** NASDAQ TotalView */
		StringID194718 = "194718" /** 全美综合报价 */
		StringID194721 = "194721" /** Nasdaq Basic(API) */
		StringID194722 = "194722" /** Nasdaq TotalView(API) */
		StringID194723 = "194723" /** 期权实时行情(API) */
		StringID194724 = "194724" /** 期权深度摆盘(API) */
		StringID194725 = "194725" /** 期权深度行情(API) */
		StringID194731 = "194731" /** CBOE Bats BZX LV2高级行情 */
		StringID194732 = "194732" /** CBOE Direct Edge LV2高级行情 */
		StringID194735 = "194735" /** 港股LV1(移动端) */
		StringID194737 = "194737" /** Nasdaq TotalView */
		StringID194738 = "194738" /** NYSE OpenBook */
		StringID194741 = "194741" /** Nasdaq TotalView（专业） */
		StringID194742 = "194742" /** NYSE OpenBook（专业） */
		StringID194743 = "194743" /** 全美综合报价（专业） */
		StringID194744 = "194744" /** OPRA期权实时行情（专业） */
		StringID194746 = "194746" /** 港股LV2+期权期货LV2行情 */
		StringID194747 = "194747" /** 港股LV2实时行情 */
		StringID194749 = "194749" /** OPRA期权深度行情（专业） */
		StringID194751 = "194751" /** 新加坡期货LV2高级行情 */
		StringID194754 = "194754" /** 新加坡期货LV1实时行情 */
		StringID194755 = "194755" /** Nasdaq Basic */
		StringID194760 = "194760" /** Nasdaq Basic+TotalView (Pro) */
		StringID194761 = "194761" /** 新加坡股票LV2高级行情 */
		StringID194762 = "194762" /** 新加坡股票LV1实时行情 */
		StringID194764 = "194764" /** CBOE Bats BZX(Pro) */
		StringID194766 = "194766" /** CBOE Direct Edge(Pro) */
		StringID194768 = "194768" /** 加拿大LV1实时行情（专业） */
		StringID194773 = "194773" /** 港股高级全盘行情 */
		StringID194776 = "194776" /** 澳洲LV2高级行情(专业) */
		StringID194777 = "194777" /** NASDAQ买卖40档及逐笔成交 */
		StringID194778 = "194778" /** NYSE买卖40档及逐笔成交 */
		StringID194783 = "194783" /** 全美综合摆盘与最新成交价 */
		StringID194787 = "194787" /** 买卖十档及逐笔成交（含窝轮牛熊） */
		StringID194788 = "194788" /** 买卖十档及逐笔成交（含全品类） */
		StringID194789 = "194789" /** 全美16家买卖价与最新成交价 */
		StringID194791 = "194791" /** 期货深度摆盘 */
		StringID194794 = "194794" /** 实时报价及逐笔成交 */
		StringID194795 = "194795" /** 纳斯达克基础报价 */
		StringID194797 = "194797" /** OPRA期权综合实时买卖价及最新成交价 */
		StringID194801 = "194801" /** 买卖四十档及逐笔成交 */
		StringID194803 = "194803" /** BATS交易所深度摆盘 */
		StringID194806 = "194806" /** Direct Edge交易所深度摆盘 */
		StringID194810 = "194810" /** 实时报价与逐笔成交 */
		StringID194812 = "194812" /** 股票报价 */
		StringID194813 = "194813" /** 买卖40档及经纪商明细 */
		StringID194817 = "194817" /** 附赠期权期货LV1行情 */
		StringID205389 = "205389" /** 行情激活信息请关注邮件及站内提示 */
		StringID205390 = "205390" /** 获取账户列表失败，请联系客服处理 */
		StringID214022 = "214022" /** 免费行情卡已发卡，请勿重复调用 */
		StringID217391 = "217391" /** "2. 有效期内购买多张同类型行情卡，有效期将累加计算；
""3. 应交易所要求，行情购买后仅授权一个设备；多台设备同时登录时，仅新登录设备生效，旧设备页面会有抢占行情提示；" */
		StringID217392 = "217392" /** "2. 有效期内购买多张同类型行情卡，有效期将累加计算；
""3. 应交易所要求，行情购买后仅授权一个设备；多台设备同时登录时，仅新登录设备生效，旧设备页面会有抢占行情提示；
""4. 仅支持人民币支付，参考汇率：1港币=" */
		StringID217393 = "217393" /** "2. 有效期内购买多张同类型行情卡，有效期将累加计算；
""3. 应交易所要求，行情购买后仅授权一个设备；多台设备同时登录时，仅新登录设备生效，旧设备页面会有抢占行情提示；
""4. 仅支持人民币支付，参考汇率：1美元=" */
		StringID217394 = "217394" /** "2. 账户在本周期结束之前的24小时内收取续期费用，请确认续期费用；
""3. 支付成功后将自动为您激活行情卡；
""4. 订阅前请阅读《自动续期订阅协议》及《隐私政策》" */
		StringID217395 = "217395" /** 行情权限已到期，如需续期，请前往： */
		StringID217397 = "217397" /** 到期按%s%s/月自动续费，可取消 */
		StringID217398 = "217398" /** 到期按%s%s/季自动续费，可取消 */
		StringID217399 = "217399" /** 到期按%s%s/年自动续费，可取消 */
		StringID217513 = "217513" /** "2. 您可以前往Google Play取消您的订阅。如果取消，您的订阅将在当前账单周期结束时停止；
""3. 应交易所要求，行情购买后仅授权一个设备；多台设备同时登录时，仅新登录设备生效，旧设备页面会有抢占行情提示；" */
		StringID217514 = "217514" /** 付费购买 */
		StringID217515 = "217515" /** 支持按月、季、年购买当前功能，期限灵活，支持自动续费。 */
		StringID217516 = "217516" /** 升级勋章 */
		StringID217517 = "217517" /** 提升勋章等级，有机会解锁该功能终身权限。 */
		StringID217518 = "217518" /** 参与限时活动 */
		StringID217519 = "217519" /** 即将上线，敬请期待。 */
		StringID217520 = "217520" /** 积分兑换 */
		StringID217521 = "217521" /** 完成任务，通过积分兑换该功能的权限。 */
		StringID217522 = "217522" /** 仅含衍生品实时行情，不包含股票 */
		StringID217523 = "217523" /** 382.67港元/月 */
		StringID217524 = "217524" /** 推荐 */
		StringID222632 = "222632" /** 今日下单次数已达上限 */
		StringID222633 = "222633" /** 今日激活次数已达上限 */
		StringID222634 = "222634" /** 今日访问次数已达上限 */
		StringID222635 = "222635" /** 今日取消订单次数已达上限 */
		StringID222636 = "222636" /** 今日提交次数已达上限 */
		StringID225314 = "225314" /** 免费日股LV1行情 */
		StringID225315 = "225315" /** 根据东京证券交易所关于日股行情的使用要求，提交以下信息，即可免费领取日股LV1实时行情 */
		StringID225316 = "225316" /** 姓名 */
		StringID225317 = "225317" /** 地址 */
		StringID225318 = "225318" /** 提交 */
		StringID225319 = "225319" /** 提交成功 */
		StringID225320 = "225320" /** 重启App后生效 */
		StringID225321 = "225321" /** LV1实时行情的著作管理权属于东京证券交易所，结合权利人要求，使用该服务需要您提供以下信息。我们会结合富途牛牛隐私政策，严格保护您的个人隐私。 */
		StringID225322 = "225322" /** LV1实时行情的著作管理权属于东京证券交易所，结合权利人要求，使用该服务需要您提供以下信息。我们会结合Moomoo隐私政策，严格保护您的个人隐私。 */
		StringID226640 = "226640" /** 操作频繁，请稍后重试 */
		StringID234040 = "234040" /** 折合%s美元 */
		StringID234140 = "234140" /** "2. 有效期内购买多张同类型行情卡，有效期将累加计算；
""3. 应交易所要求，行情购买后仅授权一个设备；多台设备同时登录时，仅新登录设备生效，旧设备页面会有抢占行情提示；
""4. 仅支持人民币支付，参考汇率：1令吉=" */
		StringID234232 = "234232" /** 折合%s人民币 */
)
