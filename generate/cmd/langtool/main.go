// 多语言工具，用于生成多语言po文件和常量文件，具体用法可参考example目录下的示例
package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"github.com/leonelquinteros/gotext"
	"github.com/urfave/cli/v2"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
)

type KV struct {
	Key   string
	Value string
}

type MultiLangResponse struct {
	Code    int32         `json:"code"`
	Message string        `json:"message"`
	Data    MultiLangData `json:"data"`
}

type MultiLangData struct {
	List map[string]map[int32]string `json:"list"`
}

var i18nDirs = map[int32]string{
	metadata.ClientLangZhCN: "zh_CN",
	metadata.ClientLangZhHK: "zh_HK",
	metadata.ClientLangEnUS: "en_US",
	metadata.ClientLangJA:   "ja",
}

const langTpl = `// Code generated by Lang Tool. DO NOT EDIT.
package {{.PackageName}}

const (
	{{- range $k, $v := .KVS}}
		{{$.ConstPrefix}}{{$v.Key}} = "{{$v.Key}}" /** {{$v.Value}} */
	{{- end}}
)
`

func main() {
	app := initCliApp()
	err := app.Run(os.Args)
	if err != nil {
		fmt.Println(err)
	}
}

func initCliApp() *cli.App {
	return &cli.App{
		Name: "多语言工具",
		Commands: []*cli.Command{
			{
				Name:  "genpo",
				Usage: "生成多语言po文件",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:     "bizTypeId",
						Aliases:  []string{"b"},
						Usage:    "多语言后台业务类型id",
						Required: true,
					},
					&cli.StringFlag{
						Name:     "path",
						Aliases:  []string{"p"},
						Usage:    "po文件输出路径",
						Required: true,
					},
				},
				Action: genPoFile,
			},
			{
				Name:  "constdef",
				Usage: "生成多语言常量文件",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:     "package",
						Aliases:  []string{"p"},
						Usage:    "go package name",
						Required: true,
					},
					&cli.StringFlag{
						Name:     "constPrefix",
						Aliases:  []string{"c"},
						Usage:    "const prefix",
						Required: true,
					},
					&cli.StringFlag{
						Name:     "inputFile",
						Aliases:  []string{"i"},
						Usage:    "po file path",
						Required: true,
					},
					&cli.StringFlag{
						Name:     "outputFile",
						Aliases:  []string{"o"},
						Usage:    "output file path",
						Required: true,
					},
				},
				Action: genConstDefFile,
			},
		},
	}
}

func genConstDefFile(c *cli.Context) error {
	var (
		packageName = c.String("p")
		constPrefix = c.String("c")
		poFilepath  = c.String("i")
		output      = c.String("o")
	)
	kvs, err := parsePoFile(poFilepath)
	if err != nil {
		return err
	}

	file, err := createFile(output)
	if err != nil {
		return err
	}
	defer file.Close()

	tpl, err := template.New("langid").Parse(langTpl)
	if err != nil {
		return err
	}

	err = tpl.Execute(file, map[string]any{
		"PackageName": packageName,
		"ConstPrefix": constPrefix,
		"KVS":         kvs,
	})
	if err != nil {
		return err
	}

	return nil
}

func genPoFile(c *cli.Context) error {
	bizTypeId := c.String("bizTypeId")
	path := c.String("path")
	data, err := fetchMultiLang(bizTypeId)
	if err != nil {
		return err
	}

	for langCode, dir := range i18nDirs {
		po := gotext.NewPo()
		for stringID, item := range data.Data.List {
			content, ok := item[langCode]
			if !ok {
				return fmt.Errorf("miss multi-lang content, langCode: %d, stringID: %s", langCode, stringID)
			}
			po.Set(stringID, content)
		}
		text, err := po.MarshalText()
		if err != nil {
			return err
		}

		outputFile := filepath.Join(path, dir, "LC_MESSAGES", "auto-gen.po")
		file, err := createFile(outputFile)
		if err != nil {
			return err
		}

		_, err = file.Write(text)
		if err != nil {
			return err
		}
	}

	return nil
}

func fetchMultiLang(bizTypeId string) (*MultiLangResponse, error) {
	values := url.Values{}
	values.Set("bizTypeId", bizTypeId)
	urlObj, _ := url.Parse("http://i18n.futuoa.com/api/pack/get-locale-pack")
	urlObj.RawQuery = values.Encode()
	resp, err := http.Get(urlObj.String())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result MultiLangResponse
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}
	if result.Code != 0 {
		return nil, fmt.Errorf("failed to get locale pack, code: %d, message: %s", result.Code, result.Message)
	}

	return &result, nil
}

func parsePoFile(path string) ([]KV, error) {
	_, err := os.Stat(path)
	if err != nil {
		return nil, err
	}

	var result []KV
	po := gotext.NewPo()
	po.ParseFile(path)

	for _, v := range po.GetDomain().GetTranslations() {
		if v.ID == "" {
			continue
		}
		if len(v.Trs) == 0 {
			return nil, fmt.Errorf("string id: %s, no translation", v.ID)
		}
		result = append(result, KV{
			Key:   v.ID,
			Value: v.Trs[0],
		})
	}
	sort.SliceStable(result, func(i, j int) bool {
		return result[i].Key < result[j].Key
	})

	return result, nil
}

func createFile(path string) (*os.File, error) {
	dir, _ := filepath.Split(path)

	fileInfo, err := os.Stat(dir)
	if err != nil {
		if !os.IsNotExist(err) {
			return nil, err
		}

		if err = os.MkdirAll(dir, os.ModePerm); err != nil {
			return nil, err
		} else {
			return os.Create(path)
		}
	}

	if !fileInfo.IsDir() {
		return nil, fmt.Errorf("%s is not a directory", path)
	}

	return os.Create(path)
}
