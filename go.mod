module gitlab.futunn.com/web_data_application/golib

go 1.20

require (
	github.com/go-kit/kit v0.13.0
	github.com/golang/protobuf v1.5.3
	github.com/leonelquinteros/gotext v1.5.2
	github.com/shopspring/decimal v1.2.0
	github.com/sony/sonyflake v1.2.0
	github.com/stretchr/testify v1.8.4
	github.com/urfave/cli/v2 v2.26.0
	gitlab.futunn.com/artifact-go/acc-info/pb v1.6.1
	gitlab.futunn.com/artifact-go/common/transaction v1.0.147
	gitlab.futunn.com/artifact-go/risk-user-account-info/pb v1.0.11
	gitlab.futunn.com/artifact-go/sba-asset-pay-server-go/pb v0.0.0-**************-c10d3c523dda
	gitlab.futunn.com/artifact-go/user_attribution/api v1.1.0
	gitlab.futunn.com/artifact-go/user_attribution/pb v1.1.0
	gitlab.futunn.com/go-libs/infra/errors v0.0.7
	gitlab.futunn.com/go-libs/infra/metadata/v2 v2.0.0
	gitlab.futunn.com/golang/fls v0.0.29
	gitlab.futunn.com/golang/fmonitor v0.2.8
	gitlab.futunn.com/infra/frpc v1.22.5
	golang.org/x/exp v0.0.0-**************-65229373498e
	gorm.io/gorm v1.25.1
)

require (
	github.com/PuerkitoBio/rehttp v1.1.0 // indirect
	github.com/StackExchange/wmi v1.2.1 // indirect
	github.com/alibaba/sentinel-golang v1.0.2 // indirect
	github.com/armon/go-metrics v0.4.0 // indirect
	github.com/cenkalti/backoff v2.2.1+incompatible // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/fatih/color v1.13.0 // indirect
	github.com/fsnotify/fsnotify v1.4.9 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.6.3 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.13.0 // indirect
	github.com/go-playground/universal-translator v0.17.0 // indirect
	github.com/go-playground/validator/v10 v10.2.0 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/gookit/color v1.5.2 // indirect
	github.com/hashicorp/consul/api v1.14.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-hclog v1.2.2 // indirect
	github.com/hashicorp/go-immutable-radix v1.3.1 // indirect
	github.com/hashicorp/go-rootcerts v1.0.2 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/hashicorp/serf v0.10.0 // indirect
	github.com/inhies/go-bytesize v0.0.0-20201103132853-d0aed0d254f8 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/knadh/koanf v1.4.2 // indirect
	github.com/leodido/go-urn v1.2.0 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.16 // indirect
	github.com/mitchellh/copystructure v1.2.0 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/mitchellh/reflectwalk v1.0.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/panjf2000/ants/v2 v2.4.3 // indirect
	github.com/pelletier/go-toml v1.7.0 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/procfs v0.7.3 // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/shirou/gopsutil v3.20.11+incompatible // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/ugorji/go/codec v1.1.7 // indirect
	github.com/valyala/fastrand v1.1.0 // indirect
	github.com/valyala/histogram v1.2.0 // indirect
	github.com/xo/terminfo v0.0.0-20210125001918-ca9a967f8778 // indirect
	github.com/xrash/smetrics v0.0.0-20201216005158-039620a65673 // indirect
	gitlab.futunn.com/artifact-go/common/aas_cmn v1.0.94 // indirect
	gitlab.futunn.com/artifact-go/common/ftstringdefine v1.0.13 // indirect
	gitlab.futunn.com/artifact-go/common/risk_comm v1.0.76 // indirect
	gitlab.futunn.com/artifact-go/common/soa_clearing_interface v1.0.153 // indirect
	gitlab.futunn.com/artifact-go/common/srpc v1.0.70 // indirect
	gitlab.futunn.com/artifact-go/common/validate v1.0.44 // indirect
	gitlab.futunn.com/artifact-go/common/web v1.0.15 // indirect
	gitlab.futunn.com/artifact-go/fns-agent/api v1.0.0 // indirect
	gitlab.futunn.com/artifact-go/fns-agent/pb v1.0.0 // indirect
	gitlab.futunn.com/artifact-go/sig/api v1.1.0 // indirect
	gitlab.futunn.com/artifact-go/sig/pb v1.1.0 // indirect
	gitlab.futunn.com/artifact-go/srpc_health/pb v1.0.4 // indirect
	gitlab.futunn.com/go-libs/auth/ticketverify v0.3.5 // indirect
	gitlab.futunn.com/go-libs/infra/appinfo v0.1.2 // indirect
	gitlab.futunn.com/go-libs/infra/backward v0.0.2 // indirect
	gitlab.futunn.com/go-libs/infra/http v0.0.2 // indirect
	gitlab.futunn.com/go-libs/infra/inet v0.0.0-20230905031048-271d08fd5b96 // indirect
	gitlab.futunn.com/go-libs/infra/pathconv v0.1.1 // indirect
	gitlab.futunn.com/golang/cmlb v0.6.0 // indirect
	gitlab.futunn.com/golang/env v0.0.2 // indirect
	gitlab.futunn.com/golang/ftrace v0.22.1 // indirect
	gitlab.futunn.com/golang/ftrace-instrumentation/frpc v1.1.0 // indirect
	gitlab.futunn.com/golang/ftrace-instrumentation/gorm v1.0.10 // indirect
	gitlab.futunn.com/golang/metric v0.0.12 // indirect
	gitlab.futunn.com/golang/monitor v1.0.1 // indirect
	gitlab.futunn.com/golang/srpc v1.1.4 // indirect
	gitlab.futunn.com/infra/futu_auth/service_auth_sdk v1.2.1 // indirect
	gitlab.futunn.com/infra/naming_service/libs/registry v1.0.0 // indirect
	gitlab.futunn.com/infra/naming_service/libs/rule v0.1.0 // indirect
	gitlab.futunn.com/infra/naming_service/libs/tag_build_rule v0.2.0 // indirect
	gitlab.futunn.com/infra/naming_service/sdk/fns_go v1.0.4 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/automaxprocs v1.3.0 // indirect
	go.uber.org/multierr v1.7.0 // indirect
	go.uber.org/zap v1.19.1 // indirect
	golang.org/x/net v0.0.0-20220722155237-a158d28d115b // indirect
	golang.org/x/sync v0.0.0-20220722155255-886fb9371eb4 // indirect
	golang.org/x/sys v0.5.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/time v0.0.0-20211116232009-f0f3c7e86c11 // indirect
	google.golang.org/protobuf v1.31.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.5.1 // indirect
)

replace (
	github.com/mitchellh/mapstructure => gitlab.futunn.com/infra/fork/mapstructure v0.0.3
	github.com/shopspring/decimal => gitlab.futunn.com/infra/fork/decimal v1.3.2
)
