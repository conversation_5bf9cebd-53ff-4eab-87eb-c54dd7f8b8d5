// Package enhancelog
// 全局日志增强中间件
package enhancelog

import (
	"context"

	"github.com/go-kit/kit/endpoint"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/infra/frpc/pkg/log"
)

type Option func(ctx context.Context) log.Field

// EnhanceLog 全局日志中间件，无需改动日志代码，添加额外字段
// 在代码里引入注册该中间件即可
// usage:
//
//	func init() {
//		middleware.AddServerFilterChain(EnhanceLog(
//			WithClientIP(),
//			WithClientType(),
//			WithClientVersion(),
//			WithClientLang(),
//		))
//	}
func EnhanceLog(opts ...Option) endpoint.Middleware {
	return func(next endpoint.Endpoint) endpoint.Endpoint {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			if len(opts) > 0 {
				fields := make([]log.Field, 0, len(opts))
				for _, opt := range opts {
					fields = append(fields, opt(ctx))
				}
				ctx = log.With(ctx, fields...)
			}

			return next(ctx, request)
		}
	}
}

func WithClientIP() Option {
	return func(ctx context.Context) log.Field {
		clientIP, _ := metadata.GetClientIpv4(ctx)
		return log.String("clientIP", clientIP.String())
	}
}

func WithClientType() Option {
	return func(ctx context.Context) log.Field {
		clientType, _ := metadata.GetClientType(ctx)
		return log.Uint32("clientType", clientType)
	}
}

func WithClientVersion() Option {
	return func(ctx context.Context) log.Field {
		clientVersion, _ := metadata.GetClientVersion(ctx)
		return log.Uint32("clientVersion", clientVersion)
	}
}

func WithClientLang() Option {
	return func(ctx context.Context) log.Field {
		clientLang, _ := metadata.GetClientLang(ctx)
		return log.Uint32("clientLang", clientLang)
	}
}

func WithUserAttribution() Option {
	return func(ctx context.Context) log.Field {
		userAttr, _ := metadata.GetUserAttribution(ctx)
		return log.Uint32("userAttribution", userAttr)
	}
}

func WithGrayFlag() Option {
	return func(ctx context.Context) log.Field {
		grayFlag, _ := metadata.GetGrayFlag(ctx)
		return log.Uint32("grayFlag", grayFlag)
	}
}

func WithCallerName() Option {
	return func(ctx context.Context) log.Field {
		callerName, _ := metadata.GetCallerName(ctx)
		return log.String("callerName", callerName)
	}
}

func WithStaffName() Option {
	return func(ctx context.Context) log.Field {
		staffName, _ := metadata.GetStaffName(ctx)
		return log.String("staffName", staffName)
	}
}
