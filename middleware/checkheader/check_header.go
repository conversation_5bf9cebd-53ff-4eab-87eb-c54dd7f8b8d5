// Package checkheader 全局日志中间件，检查srpc header字段是否存在
package checkheader

import (
	"context"

	"github.com/go-kit/kit/endpoint"
	"gitlab.futunn.com/go-libs/infra/errors"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
)

type Option func(ctx context.Context) error

// CheckHeader 全局日志中间件，检查srpc header字段是否存在
// 在代码里引入注册该中间件即可
// usage:
//
//	func init() {
//		middleware.AddServerFilterChain(CheckHeader(
//			CheckStaffName(),
//			CheckStaffId(),
//		))
//	}
func CheckHeader(opts ...Option) endpoint.Middleware {
	return func(next endpoint.Endpoint) endpoint.Endpoint {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			for _, opt := range opts {
				if err := opt(ctx); err != nil {
					return nil, err
				}
			}
			return next(ctx, request)
		}
	}
}

func CheckStaffName() Option {
	return func(ctx context.Context) error {
		staffName, ok := metadata.GetStaffName(ctx)
		if staffName == "" || !ok {
			return errors.New("miss staff name in header")
		}
		return nil
	}
}

func CheckStaffId() Option {
	return func(ctx context.Context) error {
		staffId, ok := metadata.GetStaffId(ctx)
		if staffId == 0 || !ok {
			return errors.New("miss staff id in header")
		}
		return nil
	}
}

func CheckOriginUid() Option {
	return func(ctx context.Context) error {
		originUid, ok := metadata.GetOriginUid(ctx)
		if originUid == 0 || !ok {
			return errors.New("miss origin uid in header")
		}
		return nil
	}
}
