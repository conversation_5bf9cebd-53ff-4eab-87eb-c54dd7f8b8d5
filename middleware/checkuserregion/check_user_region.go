// Package checkuserregion
// 检查用户请求是否正确路由到指定地区的服务
package checkuserregion

import (
	"context"
	"fmt"

	"github.com/go-kit/kit/endpoint"
	"github.com/golang/protobuf/proto"
	api "gitlab.futunn.com/artifact-go/user_attribution/api/userattribution"
	pb "gitlab.futunn.com/artifact-go/user_attribution/pb/userattribution"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/web_data_application/golib/util/log"
)

// CheckUserRegion 检查用户请求是否正确路由到指定地区的服务
// 使用方法：
// 1. 在配置文件中配置归属地查询服务，如：
// [frpc.client.UsrAttributionSvr]
// request_timeout = "1s"
// address = "fns://user_attribution?entity=futunn&env=dev&g_service_area=cn"
// 2. 在配置文件中配置允许的用户地区，如：
// [middleware.check_user_region]
// enabled = true
// allowed_user_regions = [1, 2, 3] # 枚举值参考 user_attribution.proto 中的 FtUserRegion
// 也可在项目中手动调用 CheckUserRegion 方法注册中间件
func CheckUserRegion(allowedRegions []pb.FtUserRegion) endpoint.Middleware {
	return func(next endpoint.Endpoint) endpoint.Endpoint {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			uid, ok := metadata.GetOriginUid(ctx)
			if !ok || uid == 0 {
				return next(ctx, request)
			}

			response, err := api.RegistAttribution(ctx, &pb.RegistAttributionReq{Uid: proto.Uint64(uid)})
			if err != nil {
				log.Error(ctx, "regist attribution failed",
					fls.ErrorField(err),
				).WithInc()
				return nil, fmt.Errorf("regist attribution failed: %w", err)
			}

			code := response.GetRetCode()
			msg := string(response.GetRetMsg())
			if code != 0 {
				log.Error(ctx, "regist attribution failed",
					fls.Int32("code", code),
					fls.String("msg", msg),
				).WithInc()
				return nil, fmt.Errorf("regist attribution failed, code: %d, msg: %s", code, msg)
			}

			userRegion := pb.FtUserRegion(response.GetRegistArea().GetRegistArea())
			for _, allowedRegion := range allowedRegions {
				if allowedRegion == userRegion {
					return next(ctx, request)
				}
			}

			log.Error(ctx, "request not allowed",
				fls.String("user_region", userRegion.String()),
				fls.Any("allowed_regions", allowedRegions),
			).WithAlert()

			return nil, fmt.Errorf("request not allowed, user region: %s, allowed regions: %v", userRegion.String(), allowedRegions)
		}
	}
}
