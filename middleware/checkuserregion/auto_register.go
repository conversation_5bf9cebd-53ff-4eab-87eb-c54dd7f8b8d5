package checkuserregion

import (
	"context"
	"errors"

	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/infra/frpc/pkg"
	"gitlab.futunn.com/infra/frpc/pkg/conf"
	"gitlab.futunn.com/infra/frpc/pkg/middleware"
	"gitlab.futunn.com/web_data_application/golib/util/log"
)

func init() {
	pkg.Register(autoRegister)
}

func autoRegister() error {
	cfg := &Config{}

	tomlConfig := conf.DefaultConfig()
	if err := tomlConfig.Unmarshal("middleware.check_user_region", cfg); err != nil {
		log.Error(context.Background(), "failed to unmarshal check_user_region config",
			fls.ErrorField(err),
		).WithInc()
	}

	if cfg.Enabled && len(cfg.AllowedUserRegions) > 0 {
		if !tomlConfig.Exist("frpc.client.UsrAttributionSvr") {
			return errors.New("frpc.client.UsrAttributionSvr not found in config")
		}

		middleware.AddServerFilterChain(
			CheckUserRegion(cfg.AllowedUserRegions),
		)
	}

	return nil
}
