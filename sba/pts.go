package sba

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang/protobuf/proto"
	"gitlab.futunn.com/artifact-go/acc-info/pb/accinfo"
	"gitlab.futunn.com/artifact-go/common/transaction/transaction"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/web_data_application/golib/money"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/account"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/scene"
)

func buildPTSTransaction(scene scene.Scene, fromAccount *accinfo.Account, amount money.Money, brokerID uint32) (*transaction.AsyncCommandMsg, error) {
	// 一期只支持MFI，其他券商直接返回nil
	if brokerID != metadata.BrokerIdFsiInc {
		return nil, nil
	}

	//if err := validateFromAccount(fromAccount); err != nil {
	//	return nil, err
	//}

	originID := getUniqueID(getAppID(scene))

	return &transaction.AsyncCommandMsg{
		Action: proto.Int32(int32(transaction.CommandType_COMMAND_TYPE_IMPORT)),
		ImportTrns: []*transaction.TransactionMeta{
			{
				TrnType:         proto.Int32(getTrnType(scene)),
				TrnSubType:      proto.Int32(getTrnSubType(scene)),
				Category:        proto.Int32(int32(transaction.TransactionCategory_CASH_MOV)),
				CashMov:         getCashMov(scene, amount),
				OriginSystem:    proto.Int32(int32(getAppID(scene))),
				OriginId:        proto.String(originID),
				UniqueId:        proto.String(originID),
				FromAccount:     getFromAccount(fromAccount),
				ContraAccount:   getContraAccount(scene),
				TradeDate:       nil, // 必填，约定好了由sba服务填充
				SettleDate:      nil, // 必填，约定好了由sba服务填充
				TransactionTime: proto.Int64(getTransactionTime()),
				Description:     proto.String(getDescription(scene)),
			},
		},
	}, nil
}

func validateFromAccount(fromAccount *accinfo.Account) error {
	if fromAccount == nil {
		return errors.New("from account is nil")
	}
	if fromAccount.GetCorrespondent() == "" {
		return errors.New("from account correspondent is empty")
	}
	if fromAccount.GetAccountNo() == "" {
		return errors.New("from account account no is empty")
	}
	if fromAccount.GetCategory() == "" {
		return errors.New("from account category is empty")
	}
	if fromAccount.GetType() != account.TypeCash && fromAccount.GetType() != account.TypeMargin {
		return fmt.Errorf("from account type is invalid: %s", fromAccount.GetType())
	}

	return nil
}

func getTrnType(s scene.Scene) int32 {
	if scene.IsQTCard(s) {
		return int32(transaction.TrnType_TRN_FEE)
	} else if scene.IsAlgoCloud(s) {
		return int32(transaction.TrnType_TRN_FEE)
	}
	return 0
}

func getTrnSubType(s scene.Scene) int32 {
	if scene.IsQTCard(s) {
		return int32(transaction.TrnSubType_TRN_SUB_QUOTE)
	} else if scene.IsAlgoCloud(s) {
		return int32(transaction.TrnSubType_TRN_SUB_ACS)
	}
	return 0
}

func getCashMov(s scene.Scene, amount money.Money) *transaction.CashMovMeta {
	// 已和jinguangxu确认，买入gross_amt为正值，退款gross_amt为负值
	grossAmt := amount.Value.String()
	if scene.IsRefund(s) {
		grossAmt = "-" + grossAmt
	}

	return &transaction.CashMovMeta{
		Currency: proto.String(amount.Currency),
		GrossAmt: proto.String(grossAmt),
	}
}

func getSubAccNo(accountType string) string {
	switch accountType {
	case account.TypeCash:
		return account.SubAccNoCash
	case account.TypeMargin:
		return account.SubAccNoMargin
	default:
		return ""
	}
}

func getUniqueID(appID uint32) string {
	return fmt.Sprintf("%d%s", appID, GenId())
}

func getTransactionTime() int64 {
	return time.Now().UnixMicro()
}

func getFromAccount(fromAccount *accinfo.Account) *transaction.Account {
	correspondent := fromAccount.GetCorrespondent()
	acctNo := fromAccount.GetAccountNo()
	subAcctNo := getSubAccNo(fromAccount.GetType())
	category := fromAccount.GetCategory()

	// todo 兼容字段缺失的情况，直接返回nil
	if correspondent == "" || acctNo == "" || subAcctNo == "" || category == "" {
		return nil
	}

	return &transaction.Account{
		Correspondent: proto.String(correspondent),
		AcctNo:        proto.String(acctNo),
		SubAcctNo:     proto.String(subAcctNo),
		AcctCategory:  proto.String(category),
	}
}

func getContraAccount(s scene.Scene) *transaction.Account {
	if scene.IsQTCard(s) {
		return &transaction.Account{
			Correspondent: proto.String("FUTU"),
			AcctNo:        proto.String("RZ0000003051"),
			SubAcctNo:     proto.String(account.SubAccNoDefault),
			AcctCategory:  proto.String("General Ledger"),
		}
	} else if scene.IsAlgoCloud(s) {
		return &transaction.Account{
			Correspondent: proto.String("FUTU"),
			AcctNo:        proto.String("RZ0000003050"),
			SubAcctNo:     proto.String(account.SubAccNoDefault),
			AcctCategory:  proto.String("General Ledger"),
		}
	}
	return nil
}
