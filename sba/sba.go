// Package sba
// sba相关业务功能包，包括构建SBARequiredInfo、校验余额、获取资产校验标志等功能
package sba

import (
	"github.com/shopspring/decimal"
	"gitlab.futunn.com/artifact-go/acc-info/pb/accinfo"
	"gitlab.futunn.com/artifact-go/common/transaction/transaction"
	riskinfopb "gitlab.futunn.com/artifact-go/risk-user-account-info/pb/riskuseraccountinfo"
	sbapay "gitlab.futunn.com/artifact-go/sba-asset-pay-server-go/pb/sbaservergo"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/web_data_application/golib/money"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/algocloud"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/errcode"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/qtcard"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/sba"
	"gitlab.futunn.com/web_data_application/golib/sba/consts/scene"
)

type SBARequiredInfo struct {
	AppID       uint32
	BizTypeID   uint32
	MarginRatio string
	APIType     string
	Comment     string
	OriginID    *string
	Transaction *transaction.AsyncCommandMsg
}

// BuildSBARequiredInfo 构建SBA请求需要的必要信息
func BuildSBARequiredInfo(scene scene.Scene, fromAccount *accinfo.Account, amount money.Money, brokerID uint32) (*SBARequiredInfo, error) {
	if err := validateScene(scene); err != nil {
		return nil, err
	}

	trans, err := buildPTSTransaction(scene, fromAccount, amount, brokerID)
	if err != nil {
		return nil, err
	}

	var originID *string
	if len(trans.GetImportTrns()) > 0 {
		originID = trans.GetImportTrns()[0].OriginId
	}
	return &SBARequiredInfo{
		OriginID:    originID,
		AppID:       getAppID(scene),
		BizTypeID:   getBizTypeID(scene),
		MarginRatio: sba.MarginRatio,
		APIType:     sba.APIType,
		Comment:     getDescription(scene),
		Transaction: trans,
	}, nil
}

func validateScene(s scene.Scene) error {
	if s != scene.QTCardPayment && s != scene.QTCardRefund &&
		s != scene.AlgoCloudPayment && s != scene.AlgoCloudRefund {
		return errcode.InvalidScene
	}
	return nil
}

func getAppID(s scene.Scene) uint32 {
	if scene.IsQTCard(s) {
		return qtcard.AppID
	} else if scene.IsAlgoCloud(s) {
		return algocloud.AppID
	}
	return 0
}

func getBizTypeID(s scene.Scene) uint32 {
	switch s {
	case scene.QTCardPayment:
		return qtcard.PaymentBizTypeID
	case scene.QTCardRefund:
		return qtcard.RefundBizTypeID
	case scene.AlgoCloudPayment:
		return algocloud.PaymentBizTypeID
	case scene.AlgoCloudRefund:
		return algocloud.RefundBizTypeID
	}
	return 0
}

func getDescription(s scene.Scene) string {
	switch s {
	case scene.QTCardPayment:
		return qtcard.PaymentComment
	case scene.QTCardRefund:
		return qtcard.RefundComment
	case scene.AlgoCloudPayment:
		return algocloud.PaymentComment
	case scene.AlgoCloudRefund:
		return algocloud.RefundComment
	}
	return ""
}

func CheckBalance(brokerID uint32, amount money.Money, riskInfos []*riskinfopb.CapitalInfo, account *accinfo.Account) error {
	var riskInfo *riskinfopb.CapitalInfo
	for _, item := range riskInfos {
		if item.GetCurrency() == amount.Currency {
			riskInfo = item
			break
		}
	}
	if riskInfo == nil {
		return errcode.ParameterInvalid
	}

	var (
		balance decimal.Decimal
		err     error
	)

	switch brokerID {
	case metadata.BrokerIdFsiHk:
		// 最大购买力：可用现金+融资金额
		balance, err = decimal.NewFromString(riskInfo.GetMaxBuypower())
	case metadata.BrokerIdFsiInc:
		// 可兑换金额
		balance, err = decimal.NewFromString(riskInfo.GetExchangeable())
	case metadata.BrokerIdFsiSg, metadata.BrokerIdFsiAu:
		// 现金可提
		balance, err = decimal.NewFromString(riskInfo.GetCashWithdraw())
	default:
		return errcode.BrokerNotSupport
	}
	if err != nil {
		return err
	}
	if balance.LessThan(amount.Value) {
		return errcode.BalanceNotEnough
	}

	return nil
}

func GetAssetCheckFlag(brokerID uint32) (sbapay.AssetCheckFlag, error) {
	switch brokerID {
	case metadata.BrokerIdFsiHk:
		return sbapay.AssetCheckFlag_MAX_BUYPOWER, nil
	case metadata.BrokerIdFsiInc:
		return sbapay.AssetCheckFlag_EXCHANGEABLE, nil
	case metadata.BrokerIdFsiSg, metadata.BrokerIdFsiAu:
		return sbapay.AssetCheckFlag_AVAILABLE_CHECK, nil
	default:
		return 0, errcode.BrokerNotSupport
	}
}
