package sba

import (
	"fmt"
	"log"
	"net"
	"os"
	"sync"
	"time"
)

// GenId 生成sba服务需要的requestId,
// copy from: https://gitlab.futunn.com/go-libs/fsm/gofsm/-/blob/master/util/genrequestId.go
func GenId() string {
	return GetFutuRequestId()
}

// 生成FUTU规范的32位Id
var (
	mutex          sync.Mutex
	sequenceNumber uint64
	udpLocalIp     net.IP
	ipPart         string
	pidPart        string
	reservedPart   = "0000"
)

// 方式1: 通过本地路由选择网络出口ip
func initUdpLocalIp() {
	conn, err := net.Dial("udp", "**********:1234")
	if err != nil {
		logger := log.New(os.Stderr, "", log.Lshortfile|log.LstdFlags)
		logger.Fatalf("failed to init util net, err: %+v", err)
	}
	defer conn.Close()
	udpLocalIp = conn.LocalAddr().(*net.UDPAddr).IP
}

func getIpPart() string {
	var tmp string
	if ipPart == "" {
		for _, number := range udpLocalIp {
			tmp = fmt.Sprintf("%s%02x", tmp, number)
		}
		ipPart = tmp // 初始化ip_part
	}
	return ipPart
}

func getPidPart() string {
	if pidPart == "" {
		pidPart = fmt.Sprintf("%04x", os.Getpid()&0xffff)
	}
	return pidPart
}

func GetFutuRequestId() string {
	mutex.Lock()
	defer mutex.Unlock()
	sequenceNumber++

	timePart := fmt.Sprintf("%08x", time.Now().Unix()&0xffffffff)
	sequencePart := fmt.Sprintf("%08x", sequenceNumber&0xffffffff)

	return fmt.Sprintf("%s%s%s%s%s", getIpPart(), getPidPart(), timePart, sequencePart, reservedPart)
}

func init() {
	initUdpLocalIp()
	fmt.Println("init try gen Id:", GetFutuRequestId())
}
