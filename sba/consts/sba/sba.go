// Package sba sba相关常量定义
package sba

const (
	// response status
	ResponseStatusSuccess   = "ok"
	ResponseStatusNoRecord  = "no_record"
	ResponseStatusForbidden = "forbidden"

	// 支付流程初始状态
	ProcedureStatusNewPay     = "new_pay"
	ProcedureStatusNewRefund  = "new_refund"
	ProcedureExtStatusWaiting = "waiting"

	ProcedureStatusEndOK     = "end_ok"     // 结束成功
	ProcedureStatusEndReject = "end_reject" // 拒绝支付

	ProcedureExtStatusPayOK    = "pay_ok"    // 支付成功
	ProcedureExtStatusRefundOK = "refund_ok" // 退款成功

	APIType     = "change_asset_v2"
	MarginRatio = "1" // 可用资金（即IMR=1的最大购买力 沿用行情卡服务的设置

	// 查询sba使用的id类型
	QueryMethodByRequestId   = "request_id"
	QueryMethodByProcedureId = "procedure_id"
)
