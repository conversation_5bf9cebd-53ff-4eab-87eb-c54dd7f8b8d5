// Package scene 场景常量定义
package scene

type Scene int

const (
	QTCardPayment    Scene = 1
	QTCardRefund     Scene = 2
	AlgoCloudPayment Scene = 3
	AlgoCloudRefund  Scene = 4
)

func IsQTCard(s Scene) bool {
	return s == QTCardPayment || s == QTCardRefund
}

func IsAlgoCloud(s Scene) bool {
	return s == AlgoCloudPayment || s == AlgoCloudRefund
}

func IsRefund(s Scene) bool {
	return s == QTCardRefund || s == AlgoCloudRefund
}
