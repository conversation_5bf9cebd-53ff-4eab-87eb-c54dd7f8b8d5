package sba

import (
	"testing"

	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
	sbapay "gitlab.futunn.com/artifact-go/sba-asset-pay-server-go/pb/sbaservergo"
)

func Test_structToQueryParams(t *testing.T) {
	res := structToQueryParams(&sbapay.CreateProcedureRefundReq{
		OaUid: proto.Uint32(100),
		ExtProcedureRefund: &sbapay.ExtProcedureRefund{
			OriProcedureId: proto.String("fffff"),
			BizTypeId:      proto.Uint32(73),
			Amount:         proto.String("8888"),
			Ccy:            proto.String("CNY"),
			Comment:        proto.String("Refund Amount for Quote Card"),
			DetailItemList: []*sbapay.DetailItem{
				{
					DetailId: proto.Uint32(1),
					Value:    proto.String("100"),
				},
				{
					DetailId: proto.Uint32(2),
					Value:    proto.String("200"),
				},
			},
		},
	}, "")
	assert.Equal(t, []string{"oa_uid=100", "ext_procedure_refund.ori_procedure_id=fffff", "ext_procedure_refund.comment=Refund Amount for Quote Card", "ext_procedure_refund.biz_type_id=73", "ext_procedure_refund.amount=8888", "ext_procedure_refund.ccy=CNY"}, res)
}
