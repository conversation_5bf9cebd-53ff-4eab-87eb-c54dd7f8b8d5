package sba

import (
	"reflect"
	"sort"
	"strconv"
	"strings"

	md5util "gitlab.futunn.com/web_data_application/golib/util/md5"
)

// CalcChecksum
// 计算校验和
// 把结构体字段转成[k1=v1 k2=v2 k3=v3]形式，按字典序升序，再用;字符join，然后计算md5
func CalcChecksum(v any) string {
	queryParams := structToQueryParams(v, "")
	sort.StringSlice(queryParams).Sort()
	tmp := strings.Join(queryParams, ";")
	return md5util.MD5(tmp)
}

func structToQueryParams(v any, prefix string) []string {
	var result []string
	values := reflect.Indirect(reflect.ValueOf(v))
	fields := values.Type()

	if values.Kind() != reflect.Struct {
		return result
	}

	for i := 0; i < fields.NumField(); i++ {
		field := fields.Field(i)
		value := values.Field(i)
		rawTag := field.Tag.Get("json")
		if rawTag == "" || rawTag == "-" {
			continue
		}
		tag := strings.Split(rawTag, ",")[0]

		key := tag
		if prefix != "" {
			key = prefix + "." + key
		}

		if value.Kind() == reflect.Ptr {
			if value.IsNil() {
				continue
			}
			value = value.Elem()
		}

		result = append(result, handleValue(value, key)...)
	}
	return result
}

// handleValue processes the value and returns the corresponding query parameters.
func handleValue(value reflect.Value, key string) []string {
	var result []string
	switch value.Kind() {
	case reflect.String:
		result = append(result, key+"="+value.String())
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		result = append(result, key+"="+strconv.FormatInt(value.Int(), 10))
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		result = append(result, key+"="+strconv.FormatUint(value.Uint(), 10))
	case reflect.Float32, reflect.Float64:
		result = append(result, key+"="+strconv.FormatFloat(value.Float(), 'f', -1, 64))
	case reflect.Struct:
		result = append(result, structToQueryParams(value.Interface(), key)...)
	}
	return result
}
