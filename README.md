# GoLib

[toc]

## 一、项目说明

提供给web数据应用组内部使用的公共库，主要维护一些通用的工具函数、中间件、可被复用的业务代码等。

### 1.1 目录结构说明

```asciidoc
├── generate
│   └── cmd
│       └── langtool                      -- 多语言文件生成工具
├── gormcallback                                -- gorm回调函数
├── middleware                                  -- frpc中间件
├── money                                       -- 金钱相关业务库
├── sba                                         -- sba相关业务库
│   └── consts
│       ├── account
│       ├── algocloud
│       ├── qtcard
│       ├── sba
│       └── scene
└── util
    ├── async                                   -- 异步处理
    ├── bitop                                   -- 位运算
    ├── bytestr                                 -- 字节数组和字符串转化
    ├── cast                                    -- 类型转换
    ├── i18n                                    -- 多语言工具函数
    ├── lang                                    -- 客户端语言处理函数
    ├── log                                     -- 日志工具函数
    ├── md5                                     -- md5加密
    ├── metric                                  -- 上报工具函数
    ├── mr                                      -- mapreduce并发库
    ├── pagination                              -- 分页工具函数
    ├── singleflight                            -- 防止缓存击穿
    └── snowflake                               -- 雪花算法
```

### 1.2 项目流水线
http://fservice.server.com/app/15763/repository/ci-pipeline

## 二、开发指引

### 2.1 依赖说明

Go版本 >= 1.20

### 2.2 快速开始

- 选择一个版本
- 在项目使用`go get gitlab.futunn.com/web_data_application/golib@tag`进行依赖导入

### 2.3 推荐开发步骤

1. 评估你需要的功能是否符合公共库的标准（见常见问题4.1）
2. 创建`feature`分支进行开发，依赖方导入`feature`分支。
3. 通过测试。
4. 发起MR，并附上CI流水线执行结果链接。
5. 发布前打`tag`，依赖方导入`tag`。
6. 线上验证通过后合并到`main`分支。

### 2.4 版本维护说明

版本管理是项目维护的重要一环。我们采用[语义化版本规范](https://semver.org/lang/zh-CN/) (Semantic Versioning) 来标记每一个发布的版本。语义化版本规范能够帮助我们清晰地表达版本间的变化，并在项目的不同阶段提供稳定性和可预测性。

具体来说，语义化版本规范使用 `主版本号.次版本号.修订号` 的格式：

- **主版本号**：当你做了不兼容的 API 修改，
- **次版本号**：当你做了向下兼容的功能性新增，
- **修订号**：当你做了向下兼容的问题修正。

如果做出了不兼容的改动，要在module名后加上v2、v3等后缀，不要在原有版本上修改。

### 2.5 注意事项

- 每次打 tag 务必补充 CHANGELOG.md
- 导入 lib 库，会将整个库的代码和全局变量编译到项目中，因此如果不是通用的变量或者函数，请谨慎加入 lib 库
   > 如果仅单个项目用到的数据或者函数，建议在项目目录中维护
- 维护好注释和单元测试，确保代码的可读性和稳定性。

## 三、编写[注释即文档](https://cloud.tencent.com/developer/article/1959696)的库代码

符合一定规则的注释会被 godoc 工具自动解析用于生成类似标准库的文档

> `godoc -http=:6060  -goroot="."` 仅为当前路径下的 go package 生成 go doc
>
> [点此](http://todo/doc)查看 main 分支代码文档

### 3.1 包注释

godoc 会解析紧跟包声明（`package xxx`）上面的注释，生成此包的 overview。

* 一个包只应当在一个文件中编写包注释， 推荐是第一个添加的文件。
* 每个文件都可以注释，但是应当与包声明用空行隔开。

### 3.2 可导出注释

godoc 会在文档中展示所有可导出的元素，因此这些元素必须添加注释

> 注释内容与 `//` 之间应该使用空格隔开

* 可导出常量
* 可导出变量
* 可导出函数
* 可导出类型
* 可导出类型中的可导出字段
* 可导出类型中的可导出方法

### 3.3 弃用声明

当一个元素不再使用，应该及时删除，或者使用 `// Deprecated:`标记删除，并告知新的解决方案

```go
// Deprecated: use NewFoo instead
func OldFoo() { ... }
```

### 3.4 代码示例

对于函数应尽量给出代码示例，可以包目录下新建 `example_test.go`，以如下格式编写代码示例：

```go
	// 为 Add 函数编写代码示例和输出
func ExampleAdd() {
   fmt.Println("result: ", Add(1, 2))
   // Output:
   // result: 3
}

// 为 Decoder.Unmarshal 编写代码示例
func ExampleDecoder_Unmarshal() {
   var decoder Decoder
   docoder.Unmarshal(xxx)
}

// 更多示例可以参考 go 标准库

```

## 四、常见问题 (FAQ)

4.1 什么样的代码可以提交到公共库？

* 可被复用的工具函数
* 可被复用的中间件
* 可被复用的数据结构
* 可被复用的错误码定义
* 可被复用的常量定义
* 可被复用的业务代码块
* 等等可被复用的代码