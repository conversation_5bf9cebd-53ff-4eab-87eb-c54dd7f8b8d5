// Package log 提供了日志的封装，可额外提供metric的封装
package log

import (
	"context"

	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/web_data_application/golib/util/metric"
)

const skip = 3 // 由于多包了两层，需要修改一下skip，不然打印的文件行数就不准确了

type logMetric struct {
	msg string
}

func (l logMetric) WithInc(labels ...metric.KV) {
	metric.Inc(l.msg, labels...)
}

func (l logMetric) WithAlert(labels ...metric.KV) {
	metric.Alert(l.msg, labels...)
}

func Error(ctx context.Context, msg string, fields ...fls.Field) logMetric {
	return log(ctx, fls.ErrorLvl, skip, msg, fields...)
}

func Warn(ctx context.Context, msg string, fields ...fls.Field) logMetric {
	return log(ctx, fls.WarnLvl, skip, msg, fields...)
}

func Info(ctx context.Context, msg string, fields ...fls.Field) logMetric {
	return log(ctx, fls.InfoLvl, skip, msg, fields...)
}

func Debug(ctx context.Context, msg string, fields ...fls.Field) logMetric {
	return log(ctx, fls.DebugLvl, skip, msg, fields...)
}

func log(ctx context.Context, level fls.Level, skip int, msg string, fields ...fls.Field) logMetric {
	logger := getLogger(ctx)
	logger.LogWithSkip(level, skip, msg, nil, fields)
	return logMetric{msg: msg}
}

func getLogger(ctx context.Context) *fls.Logger {
	logger, ok := fls.FromContext(ctx)
	if !ok {
		logger = fls.DefaultLogger()
	}
	return logger
}
