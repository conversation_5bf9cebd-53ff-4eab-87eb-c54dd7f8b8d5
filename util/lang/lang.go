// Package lang 获取多语言常量
package lang

import (
	"context"

	"gitlab.futunn.com/go-libs/infra/metadata/v2"
)

var supportedLanguages = [...]bool{
	metadata.ClientLangZhCN: true,
	metadata.ClientLangZhHK: true,
	metadata.ClientLangEnUS: true,
	metadata.ClientLangJA:   true,
}

const defaultLanguage = metadata.ClientLangEnUS

func GetClientLangOrDefault(ctx context.Context) uint32 {
	languageType, _ := metadata.GetClientLang(ctx)
	if supportedLanguages[languageType] {
		return languageType
	}
	return defaultLanguage
}
