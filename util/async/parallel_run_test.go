package async

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 测试没有错误的情况
func TestParallelRunNotReuseGoroutine_NoErrors(t *testing.T) {
	ctx := context.Background()
	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	start := time.Now()

	handler := func(ctx context.Context, input int) (int, error) {
		time.Sleep(3 * time.Second) // 模拟处理时间
		return input * 2, nil
	}

	results := parallelRunNotReuseGoroutine(ctx, concurrency, inputs, handler)

	end := time.Now()

	// 期望执行时间小于15秒
	assert.True(t, end.Sub(start) < time.Duration(len(inputs)*3)*time.Second, "expected execution time less than 15 seconds, got %v", end.Sub(start))

	expectedResults := []int{2, 4, 6, 8, 10}
	for i, result := range results {
		if result.Err != nil {
			t.<PERSON><PERSON><PERSON>("expected no error, got %v", result.Err)
		}
		if result.Value != expectedResults[i] {
			t.<PERSON><PERSON><PERSON>("expected result %d, got %d", expectedResults[i], result.Value)
		}
	}
}

// 测试有错误的情况
func TestParallelRunNotReuseGoroutine_WithErrors(t *testing.T) {
	ctx := context.Background()
	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	handler := func(ctx context.Context, input int) (int, error) {
		if input == 3 {
			return 0, errors.New("error on input 3")
		}
		return input * 2, nil
	}

	results := parallelRunNotReuseGoroutine(ctx, concurrency, inputs, handler)

	expectedResults := []int{2, 4, 0, 8, 10}
	for i, result := range results {
		if i == 2 {
			if result.Err == nil || result.Err.Error() != "error on input 3" {
				t.Errorf("expected error 'error on input 3', got %v", result.Err)
			}
		} else {
			if result.Err != nil {
				t.Errorf("expected no error, got %v", result.Err)
			}
			if result.Value != expectedResults[i] {
				t.Errorf("expected result %d, got %d", expectedResults[i], result.Value)
			}
		}
	}
}

// 测试上下文超时的情况
func TestParallelRunNotReuseGoroutine_ContextTimeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	handler := func(ctx context.Context, input int) (int, error) {
		time.Sleep(2 * time.Second) // 模拟处理时间超过上下文超时
		return input * 2, nil
	}

	results := parallelRunNotReuseGoroutine(ctx, concurrency, inputs, handler)

	for _, result := range results {
		assert.Equal(t, context.DeadlineExceeded, result.Err, "expected context deadline exceeded error, got %v", result.Err)
	}
}

// 测试上下文取消的情况
func TestParallelRunNotReuseGoroutine_ContextCancel(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	// 立即cancel掉
	cancel()

	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	handler := func(ctx context.Context, input int) (int, error) {
		return input * 2, nil
	}

	results := parallelRunNotReuseGoroutine(ctx, concurrency, inputs, handler)

	for _, result := range results {
		assert.Equal(t, context.Canceled, result.Err, "expected context canceled error, got %v", result.Err)
	}
}

// 测试没有错误的情况
func TestParallelRunReuseGoroutine_NoErrors(t *testing.T) {
	ctx := context.Background()
	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	start := time.Now()

	handler := func(ctx context.Context, input int) (int, error) {
		time.Sleep(3 * time.Second) // 模拟处理时间
		return input * 2, nil
	}

	results := parallelRunReuseGoroutine(ctx, concurrency, inputs, handler)

	end := time.Now()

	// 期望执行时间小于15秒
	assert.True(t, end.Sub(start) < time.Duration(len(inputs)*3)*time.Second, "expected execution time less than 15 seconds, got %v", end.Sub(start))

	expectedResults := []int{2, 4, 6, 8, 10}
	for i, result := range results {
		if result.Err != nil {
			t.Errorf("expected no error, got %v", result.Err)
		}
		if result.Value != expectedResults[i] {
			t.Errorf("expected result %d, got %d", expectedResults[i], result.Value)
		}
	}
}

// 测试有错误的情况
func TestParallelRunReuseGoroutine_WithErrors(t *testing.T) {
	ctx := context.Background()
	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	handler := func(ctx context.Context, input int) (int, error) {
		if input == 3 {
			return 0, errors.New("error on input 3")
		}
		return input * 2, nil
	}

	results := parallelRunReuseGoroutine(ctx, concurrency, inputs, handler)

	expectedResults := []int{2, 4, 0, 8, 10}
	for i, result := range results {
		if i == 2 {
			if result.Err == nil || result.Err.Error() != "error on input 3" {
				t.Errorf("expected error 'error on input 3', got %v", result.Err)
			}
		} else {
			if result.Err != nil {
				t.Errorf("expected no error, got %v", result.Err)
			}
			if result.Value != expectedResults[i] {
				t.Errorf("expected result %d, got %d", expectedResults[i], result.Value)
			}
		}
	}
}

// 测试上下文超时的情况
func TestParallelRunReuseGoroutine_ContextTimeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	handler := func(ctx context.Context, input int) (int, error) {
		time.Sleep(2 * time.Second) // 模拟处理时间超过上下文超时
		return input * 2, nil
	}

	results := parallelRunReuseGoroutine(ctx, concurrency, inputs, handler)

	for _, result := range results {
		assert.Equal(t, context.DeadlineExceeded, result.Err, "expected context deadline exceeded error, got %v", result.Err)
	}
}

// 测试上下文取消的情况
func TestParallelRunReuseGoroutine_ContextCancel(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	// 立即cancel掉
	cancel()

	inputs := []int{1, 2, 3, 4, 5}
	concurrency := 3

	handler := func(ctx context.Context, input int) (int, error) {
		return input * 2, nil
	}

	results := parallelRunReuseGoroutine(ctx, concurrency, inputs, handler)

	for _, result := range results {
		assert.Equal(t, context.Canceled, result.Err, "expected context canceled error, got %v", result.Err)
	}
}

func Test_shouldReuseGoroutine(t *testing.T) {
	type args struct {
		concurrency int
		inputsLen   int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Concurrency less than inputsLen and inputsLen greater than 1000",
			args: args{concurrency: 5, inputsLen: 1500},
			want: true,
		},
		{
			name: "Concurrency equal to inputsLen",
			args: args{concurrency: 1000, inputsLen: 1000},
			want: false,
		},
		{
			name: "Concurrency greater than inputsLen",
			args: args{concurrency: 2000, inputsLen: 1000},
			want: false,
		},
		{
			name: "InputsLen less than or equal to 1000, regardless of concurrency",
			args: args{concurrency: 5, inputsLen: 1000},
			want: false,
		},
		{
			name: "Concurrency less than inputsLen but inputsLen less than 1000",
			args: args{concurrency: 5, inputsLen: 999},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, shouldReuseGoroutine(tt.args.concurrency, tt.args.inputsLen), "shouldReuseGoroutine(%v, %v)", tt.args.concurrency, tt.args.inputsLen)
		})
	}
}
