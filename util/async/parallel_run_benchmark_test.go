package async

import (
	"context"
	"fmt"
	"testing"
	"time"
)

// 示例 handler 函数
func exampleHandler(ctx context.Context, input int) (int, error) {
	// 模拟一个长时间运行的任务
	time.Sleep(1 * time.Microsecond)
	return input * 2, nil
}

// generateInputs 生成指定大小的数据集
func generateInputs(size int) []int {
	inputs := make([]int, size)
	for i := range inputs {
		inputs[i] = i
	}
	return inputs
}

var (
	inputsSize      = []int{1000, 10000, 100000, 1000000}
	concurrencyList = []int{5, 10, 20, 50, 100, 200}
)

// 基准测试 ParallelRun
func BenchmarkParallelRun(b *testing.B) {
	ctx := context.Background()

	for _, size := range inputsSize {
		for _, concurrency := range concurrencyList {
			b.Run(fmt.Sprintf("Size%d_Concurrency%d", size, concurrency), func(b *testing.B) {
				inputs := generateInputs(size)

				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					ParallelRun(ctx, concurrency, inputs, exampleHandler)
				}
			})
		}
	}
}

// 基准测试 parallelRunNotReuseGoroutine
func BenchmarkParallelRunNotReuseGoroutine(b *testing.B) {
	ctx := context.Background()

	for _, size := range inputsSize {
		for _, concurrency := range concurrencyList {
			b.Run(fmt.Sprintf("Size%d_Concurrency%d", size, concurrency), func(b *testing.B) {
				inputs := generateInputs(size)

				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					parallelRunNotReuseGoroutine(ctx, concurrency, inputs, exampleHandler)
				}
			})
		}
	}
}

// 基准测试 parallelRunReuseGoroutine
func BenchmarkParallelRunReuseGoroutine(b *testing.B) {
	ctx := context.Background()

	for _, size := range inputsSize {
		for _, concurrency := range concurrencyList {
			b.Run(fmt.Sprintf("Size%d_Concurrency%d", size, concurrency), func(b *testing.B) {
				inputs := generateInputs(size)

				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					parallelRunReuseGoroutine(ctx, concurrency, inputs, exampleHandler)
				}
			})
		}
	}
}
