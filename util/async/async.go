// Package async 异步工具包
package async

import (
	"context"
	"runtime"

	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/web_data_application/golib/util/log"
)

// Go 启动一个goroutine执行fn函数, 并捕获panic
// api使用的实践参考:https://futu.feishu.cn/docx/NCdwd2qFso0buPxbX7HcLo7bnsb
func Go(ctx context.Context, fn func(ctx context.Context) error) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				var buf [4096]byte
				n := runtime.Stack(buf[:], false)
				log.Error(ctx, "customer_goroutine_panic",
					fls.Any("recover_info", r),
					fls.String("panic_stack", string(buf[:n])),
				).WithAlert()
			}
		}()

		if err := fn(ctx); err != nil {
			log.Info(ctx, "customer_fn_return_error",
				fls.ErrorField(err),
			).WithInc()
		}
	}()
}
