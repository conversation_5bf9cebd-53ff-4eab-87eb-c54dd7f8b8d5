package async

import (
	"context"
	"sync"
)

// HandlerFunc 定义了处理单个数据项的函数类型
type HandlerFunc[T any, R any] func(context.Context, T) (R, error)

// task 结构体包含任务的数据和处理结果的索引
type task[T any] struct {
	index int
	input T
}

// Result 结构体包含处理结果和可能的错误
type Result[R any] struct {
	Value R
	Err   error
}

type ParallelRunResult[R any] struct {
	results []Result[R]
}

// ExtractValues 从结果中提取值或错误，有任意一个结果出错则返回错误
func (p *ParallelRunResult[R]) ExtractValues() ([]R, error) {
	if p == nil {
		return nil, nil
	}

	values := make([]R, len(p.results))
	for i, result := range p.results {
		if result.Err != nil {
			return nil, result.Err
		}
		values[i] = result.Value
	}
	return values, nil
}

// Results 返回所有结果
func (p *ParallelRunResult[R]) Results() []Result[R] {
	if p == nil {
		return nil
	}
	return p.results
}

// Errors 返回所有错误
func (p *ParallelRunResult[R]) Errors() []error {
	if p == nil {
		return nil
	}

	var errs []error
	for _, result := range p.results {
		if result.Err != nil {
			errs = append(errs, result.Err)
		}
	}
	return errs
}

// ParallelRun 处理一批数据，支持并发调用处理函数，并返回每个任务的结果和错误
// 返回的结果顺序与输入数据顺序一致
// 如果上下文被取消或超时，将返回上下文错误
// api使用的实践参考:https://futu.feishu.cn/docx/NCdwd2qFso0buPxbX7HcLo7bnsb
func ParallelRun[T any, R any](ctx context.Context, concurrency int, inputs []T, handler HandlerFunc[T, R]) *ParallelRunResult[R] {
	if concurrency <= 0 {
		return nil
	}
	inputsLen := len(inputs)
	if inputsLen == 0 {
		return nil
	}

	var results []Result[R]
	if shouldReuseGoroutine(concurrency, inputsLen) {
		results = parallelRunReuseGoroutine(ctx, concurrency, inputs, handler)
	} else {
		results = parallelRunNotReuseGoroutine(ctx, concurrency, inputs, handler)
	}

	return &ParallelRunResult[R]{results: results}
}

func shouldReuseGoroutine(concurrency int, inputsLen int) bool {
	return concurrency < inputsLen && inputsLen > 1000
}

// 不重用goroutine
func parallelRunNotReuseGoroutine[T any, R any](ctx context.Context, concurrency int, inputs []T, handler HandlerFunc[T, R]) []Result[R] {
	var (
		wg      sync.WaitGroup
		results = make([]Result[R], len(inputs))
		sem     = make(chan struct{}, concurrency)
	)

	for i, item := range inputs {
		tmpI, tmpItem := i, item // 避免迭代变量捕获
		wg.Add(1)
		Go(ctx, func(ctx context.Context) error {
			defer wg.Done()
			sem <- struct{}{}
			defer func() { <-sem }()
			select {
			case <-ctx.Done():
				// 如果上下文已取消或超时，记录错误
				results[tmpI] = Result[R]{Err: ctx.Err()}
				return ctx.Err()
			default:
				// 这里有个潜在问题，如果在handler执行中ctx超时了，本方法是不会感知到的，
				// 需要handler内部有超时机制主动返回，不然还是会等到handler执行完才返回
				// 可以用两个goroutine和done channel来解决这个问题，不过就太复杂了
				rsp, err := handler(ctx, tmpItem)
				// 兜底检测超时，运行前没超时，运行后超时了，也当成超时处理
				if ctx.Err() != nil {
					results[tmpI] = Result[R]{Err: ctx.Err()}
					return ctx.Err()
				} else {
					results[tmpI] = Result[R]{Value: rsp, Err: err}
					return err
				}
			}
		})
	}
	wg.Wait()

	return results
}

// 重用goroutine
func parallelRunReuseGoroutine[T any, R any](ctx context.Context, concurrency int, inputs []T, handler HandlerFunc[T, R]) []Result[R] {
	var wg sync.WaitGroup
	results := make([]Result[R], len(inputs))
	taskChan := make(chan task[T])

	wg.Add(concurrency)
	// 消费者，创建固定数量的工作goroutine
	for i := 0; i < concurrency; i++ {
		Go(ctx, func(ctx context.Context) error {
			defer wg.Done()
			for t := range taskChan {
				select {
				case <-ctx.Done():
					results[t.index] = Result[R]{Err: ctx.Err()}
				default:
					// 这里有个潜在问题，如果在handler执行中ctx超时了，本方法是不会感知到的，
					// 需要handler内部有超时机制主动返回，不然还是会等到handler执行完才返回
					// 可以用两个goroutine和done channel来解决这个问题，不过就太复杂了
					rsp, err := handler(ctx, t.input)
					// 兜底检测超时，运行前没超时，运行后超时了，也当成超时处理
					if ctx.Err() != nil {
						results[t.index] = Result[R]{Err: ctx.Err()}
					} else {
						results[t.index] = Result[R]{Value: rsp, Err: err}
					}
				}
			}
			return nil
		})
	}

	// dispatch, 生产者发送任务到任务通道
	go func() {
		for i, input := range inputs {
			taskChan <- task[T]{index: i, input: input}
		}
		close(taskChan)
	}()

	// 等待所有工作 goroutine 完成
	wg.Wait()

	return results
}
