// Package sonyflake
// 对sonyflake库的简易封装
package sonyflake

import (
	"errors"
	"fmt"
	"math"
	"net"
	"os"

	"github.com/sony/sonyflake"
)

var (
	ErrStartTimeAhead   = sonyflake.ErrStartTimeAhead
	ErrNoPrivateAddress = sonyflake.ErrNoPrivateAddress
	ErrOverTimeLimit    = sonyflake.ErrOverTimeLimit
	ErrInvalidMachineID = sonyflake.ErrInvalidMachineID
	ErrIPNotAvailable   = errors.New("ip not available")
	ErrIDOverflowInt64  = errors.New("id overflow int64")
)

var defaultGenerator *sonyflake.Sonyflake

// GenerateUint64 生成一个uint64类型的id
func GenerateUint64() (uint64, error) {
	return defaultGenerator.NextID()
}

// GenerateInt64 生成一个int64类型的id
func GenerateInt64() (int64, error) {
	id, err := defaultGenerator.NextID()
	if err != nil {
		return 0, err
	}
	// 只是一个兜底，实际上该库的readme明确说明了是不会溢出的
	if id > math.MaxInt64 {
		return 0, ErrIDOverflowInt64
	}

	return int64(id), nil
}

func init() {
	var err error
	defaultGenerator, err = sonyflake.New(sonyflake.Settings{
		MachineID:      getMachineID,
		CheckMachineID: checkMachineID,
	})
	if err != nil {
		panic(err)
	}
}

func getMachineID() (uint16, error) {
	ip, err := getLocalIP()
	if err != nil {
		return 0, err
	}
	machineID := uint16(ip[2])<<8 + uint16(ip[3])
	fmt.Printf("sonyflake_util: local_ip: %s, machineID: %d\n", ip.String(), machineID)

	return machineID, nil
}

func checkMachineID(id uint16) bool {
	return true
}

func getLocalIP() (net.IP, error) {
	if ip := net.ParseIP(os.Getenv("POD_IP")); ip != nil {
		return ip.To4(), nil
	}

	as, err := net.InterfaceAddrs()
	if err != nil {
		return nil, err
	}

	for _, a := range as {
		ipNet, ok := a.(*net.IPNet)
		if !ok || ipNet.IP.IsLoopback() {
			continue
		}

		if ipv4 := ipNet.IP.To4(); len(ipv4) == net.IPv4len {
			return ipv4, nil
		}
	}

	return nil, ErrIPNotAvailable
}
