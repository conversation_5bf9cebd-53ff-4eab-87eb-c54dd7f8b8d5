// Package pagination
// 分页工具包
package pagination

type Pagination struct {
	page     int
	pageSize int
	total    int
}

func New(page, pageSize int) *Pagination {
	return &Pagination{page: page, pageSize: pageSize}
}

func (p *Pagination) Page() int {
	return p.page
}

func (p *Pagination) SetPage(page int) *Pagination {
	p.page = page
	return p
}

func (p *Pagination) PageSize() int {
	return p.pageSize
}

func (p *Pagination) SetPageSize(pageSize int) *Pagination {
	p.pageSize = pageSize
	return p
}

func (p *Pagination) Total() int {
	return p.total
}

func (p *Pagination) SetTotal(total int) *Pagination {
	p.total = total
	return p
}

func (p *Pagination) Limit() int {
	return p.pageSize
}

func (p *Pagination) Offset() int {
	return p.page * p.pageSize
}

func (p *Pagination) Clone() *Pagination {
	return &Pagination{
		page:     p.page,
		pageSize: p.pageSize,
		total:    p.total,
	}
}
