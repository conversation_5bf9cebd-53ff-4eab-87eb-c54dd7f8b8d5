package location

import (
	"fmt"

	"gitlab.futunn.com/infra/frpc/pkg"
	"gitlab.futunn.com/infra/frpc/pkg/conf"
)

type locationCfg struct {
	Location Location `toml:"location"`
}

var cfg locationCfg

func initLocationCfg() error {
	err := conf.DefaultConfig().Unmarshal("location_cfg", &cfg)
	if err != nil {
		return err
	}
	if !isValidLoc(cfg.Location) {
		return fmt.Errorf("invalid location cfg: %s", cfg.Location)
	}
	return nil
}

var supportedLocs = map[Location]struct{}{
	CN: {}, HK: {}, US: {}, SG: {},
	AU: {}, JP: {}, MY: {}, CA: {},
}

func isValidLoc(loc Location) bool {
	_, ok := supportedLocs[loc]
	return ok
}

func init() {
	pkg.Register(initLocationCfg)
}
