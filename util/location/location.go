// Package location 支持更细粒度的地域配置
package location

type Location string

const (
	CN = Location("CN")
	HK = Location("HK")
	US = Location("US")
	SG = Location("SG")
	AU = Location("AU")
	JP = Location("JP")
	MY = Location("MY")
	CA = Location("CA")
)

// GetLocation frpc提供的loc方法无法区分CN和HK。这里额外封装一个方法，获取配置文件中的location
// 使用前需在frpc配置文件中增加location配置，如下：
// [location_cfg]
// location = "HK"
func GetLocation() Location {
	return cfg.Location
}
