package metric

type Counter struct {
	eventName string
	labels    []KV
}

func NewCounter(eventName string, labels ...KV) *Counter {
	return &Counter{eventName: eventName, labels: labels}
}

func (c *Counter) Inc(otherLabels ...KV) {
	labels := make([]KV, 0, len(c.labels)+len(otherLabels))
	labels = append(labels, c.labels...)
	labels = append(labels, otherLabels...)
	Inc(c.eventName, labels...)
}

func (c *Counter) Alert(otherLabels ...KV) {
	labels := make([]KV, 0, len(c.labels)+len(otherLabels))
	labels = append(labels, c.labels...)
	labels = append(labels, otherLabels...)
	Alert(c.eventName, labels...)
}
