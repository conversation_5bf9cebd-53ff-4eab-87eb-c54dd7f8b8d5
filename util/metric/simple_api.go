// Package metric
// metric包提供了对metric的封装，提供了Inc和Alert两个方法，用于统计事件的次数
package metric

import (
	"gitlab.futunn.com/golang/fmonitor/pkg/metric"
	"gitlab.futunn.com/golang/fmonitor/pkg/metric/label"
)

type KV struct {
	Key   string
	Value string
}

func Inc(eventName string, otherLabels ...KV) {
	allLabel := make([]string, 0, len(otherLabels)*2+2)
	allLabel = append(allLabel, label.EventName, eventName)
	for _, kv := range otherLabels {
		allLabel = append(allLabel, kv.Key, kv.Value)
	}
	metric.EventsTotal.WithLabels(allLabel...).Inc()
}

func Alert(eventName string, otherLabels ...KV) {
	allLabel := make([]string, 0, len(otherLabels)*2+4)
	allLabel = append(allLabel, label.EventName, eventName)
	allLabel = append(allLabel, label.EventLevel, "alert")
	for _, kv := range otherLabels {
		allLabel = append(allLabel, kv.Key, kv.Value)
	}
	metric.EventsTotal.WithLabels(allLabel...).Inc()
}
