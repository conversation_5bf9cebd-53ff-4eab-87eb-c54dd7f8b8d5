// Package i18n 多语言工具包
package i18n

import (
	"context"
	"fmt"

	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/infra/frpc/pkg/i18n"
	"gitlab.futunn.com/web_data_application/golib/util/log"
)

var supportedLangCodes = [...]bool{
	metadata.ClientLangZhCN: true,
	metadata.ClientLangZhHK: true,
	metadata.ClientLangEnUS: true,
	metadata.ClientLangJA:   true,
}

const defaultLangCode = metadata.ClientLangEnUS

// GetI18nVal 根据context参数和多语言id，获取对应的多语言，默认用英文兜底
func GetI18nVal(ctx context.Context, key string) string {
	langCode, _ := metadata.GetClientLang(ctx)
	if supportedLangCodes[langCode] {
		langCode = defaultLangCode
	}

	return GetI18nValByLangCode(langCode, key)
}

// GetI18nFormat 根据id获取多语言模板，并格式化
// 例如：GetI18nFormat(ctx, "i18n_id", "param1", "param2")
// 会根据id获取多语言模板，然后将模板中的%s替换为param1和param2
func GetI18nFormat(ctx context.Context, key string, params ...any) string {
	tpl := GetI18nVal(ctx, key)
	return fmt.Sprintf(tpl, params...)
}

// GetI18nValByLangCode 根据语言参数和多语言id，获取对应的多语言，默认用英文兜底
func GetI18nValByLangCode(langCode uint32, key string) string {
	content := i18n.GetLocaleById(langCode).Get(key)
	// 这里额外做一下校验，防止多语言遗漏
	if content == "" || content == key {
		log.Error(context.Background(), // 这里没传ctx，用的是background
			"failed_to_get_i18n_content",
			fls.Uint32("lang_code", langCode),
			fls.String("key", key),
		).WithInc()
	}
	return content
}

// GetI18nFormatByLangCode 根据id获取多语言模板，并格式化
// 例如：GetI18nFormatByLangCode(1, "i18n_id", "param1", "param2")
// 会根据id获取多语言模板，然后将模板中的%s替换为param1和param2
func GetI18nFormatByLangCode(langCode uint32, key string, params ...any) string {
	tpl := GetI18nValByLangCode(langCode, key)
	return fmt.Sprintf(tpl, params...)
}
