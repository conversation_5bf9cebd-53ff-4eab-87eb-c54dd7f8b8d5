package bitop

import (
	"golang.org/x/exp/constraints"
	"reflect"
	"testing"
)

func TestUint2Bytes(t *testing.T) {
	type args struct {
		bit uint32
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "test1",
			args: args{
				bit: 1,
			},
			want: []byte{1},
		},
		{
			name: "test2",
			args: args{
				bit: 3,
			},
			want: []byte{1, 2},
		},
		{
			name: "test2",
			args: args{
				bit: 3,
			},
			want: []byte{1, 2},
		},
		{
			name: "test3",
			args: args{
				bit: 13,
			},
			want: []byte{1, 4, 8},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Uint2Bytes(tt.args.bit); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Uint2Bytes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBytes2Uint(t *testing.T) {
	type args struct {
		arr []byte
	}
	type testCase[T constraints.Unsigned] struct {
		name string
		args args
		want T
	}
	tests := []testCase[uint32]{
		{
			name: "test1",
			args: args{
				arr: []byte{1},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Bytes2Uint[uint32](tt.args.arr); got != tt.want {
				t.Errorf("Bytes2Uint() = %v, want %v", got, tt.want)
			}
		})
	}
}
