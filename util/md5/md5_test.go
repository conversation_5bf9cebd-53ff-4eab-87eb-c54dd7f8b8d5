package md5

import "testing"

func TestMD5(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test with hello",
			args: args{str: "hello"},
			want: "5d41402abc4b2a76b9719d911017c592",
		},
		{
			name: "Test with world",
			args: args{str: "world"},
			want: "7d793037a0760186574b0282f2f435e7",
		},
		{
			name: "Test with GoLang",
			args: args{str: "GoLang"},
			want: "bcc49d9106002ecd42c46907172a826c",
		},
		{
			name: "Test with empty string",
			args: args{str: ""},
			want: "d41d8cd98f00b204e9800998ecf8427e",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MD5(tt.args.str); got != tt.want {
				t.Errorf("MD5() = %v, want %v", got, tt.want)
			}
		})
	}
}
