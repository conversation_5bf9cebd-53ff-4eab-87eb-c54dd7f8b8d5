package mr

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

var fn1 = func() error {
	time.Sleep(3 * time.Second)
	fmt.Println("fn1 done.")
	return nil
}

var fn2 = func() error {
	time.Sleep(5 * time.Second)
	fmt.Println("fn2 done.")
	return nil
}

func TestFinish(t *testing.T) {
	start := time.Now()
	err := Finish(fn1, fn2)
	end := time.Now()

	assert.Nil(t, err)
	assert.True(t, end.Sub(start) < 8*time.Second)
}
