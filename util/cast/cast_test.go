package cast

import (
	"golang.org/x/exp/constraints"
	"reflect"
	"testing"
)

func TestInt2Bool(t *testing.T) {
	type args[T constraints.Integer] struct {
		x T
	}
	type testCase[T constraints.Integer] struct {
		name string
		args args[T]
		want bool
	}
	tests := []testCase[int32]{
		{
			name: "test1",
			args: args[int32]{
				x: 1,
			},
			want: true,
		},
		{
			name: "test2",
			args: args[int32]{
				x: 11,
			},
			want: true,
		},
		{
			name: "test3",
			args: args[int32]{
				x: 0,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Int2Bool[int32](tt.args.x); got != tt.want {
				t.Errorf("Int2Bool() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBool2Int(t *testing.T) {
	type args struct {
		x bool
	}
	type testCase[T constraints.Integer] struct {
		name string
		args args
		want T
	}
	tests := []testCase[int64]{
		{
			name: "test1",
			args: args{
				x: true,
			},
			want: 1,
		},
		{
			name: "test2",
			args: args{
				x: false,
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Bool2Int[int64](tt.args.x); got != tt.want {
				t.Errorf("Bool2Int() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCastInts(t *testing.T) {
	type args[T constraints.Integer] struct {
		arr []T
	}
	type testCase[T constraints.Integer, R constraints.Integer] struct {
		name string
		args args[T]
		want []R
	}
	tests := []testCase[int8, int64]{
		{
			name: "test1",
			args: args[int8]{
				arr: []int8{1, 2, 3},
			},
			want: []int64{1, 2, 3},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CastInts[int8, int64](tt.args.arr); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CastInts() = %v, want %v", got, tt.want)
			}
		})
	}
}
